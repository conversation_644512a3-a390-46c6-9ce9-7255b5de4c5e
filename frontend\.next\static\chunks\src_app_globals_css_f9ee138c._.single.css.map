{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/src/app/globals.css"], "sourcesContent": ["/*! tailwindcss v4.1.10 | MIT License | https://tailwindcss.com */\n@layer properties;\n@layer theme, base, components, utilities;\n@layer theme {\n  :root, :host {\n    --font-serif: ui-serif, Georgia, Cambria, \"Times New Roman\", Times, serif;\n    --color-red-50: oklch(97.1% 0.013 17.38);\n    --color-red-100: oklch(93.6% 0.032 17.717);\n    --color-red-200: oklch(88.5% 0.062 18.334);\n    --color-red-400: oklch(70.4% 0.191 22.216);\n    --color-red-500: oklch(63.7% 0.237 25.331);\n    --color-red-600: oklch(57.7% 0.245 27.325);\n    --color-red-800: oklch(44.4% 0.177 26.899);\n    --color-red-900: oklch(39.6% 0.141 25.723);\n    --color-orange-50: oklch(98% 0.016 73.684);\n    --color-orange-400: oklch(75% 0.183 55.934);\n    --color-orange-500: oklch(70.5% 0.213 47.604);\n    --color-orange-600: oklch(64.6% 0.222 41.116);\n    --color-yellow-100: oklch(97.3% 0.071 103.193);\n    --color-yellow-200: oklch(94.5% 0.129 101.54);\n    --color-yellow-400: oklch(85.2% 0.199 91.936);\n    --color-yellow-500: oklch(79.5% 0.184 86.047);\n    --color-yellow-600: oklch(68.1% 0.162 75.834);\n    --color-yellow-700: oklch(55.4% 0.135 66.442);\n    --color-yellow-800: oklch(47.6% 0.114 61.907);\n    --color-yellow-900: oklch(42.1% 0.095 57.708);\n    --color-green-50: oklch(98.2% 0.018 155.826);\n    --color-green-100: oklch(96.2% 0.044 156.743);\n    --color-green-200: oklch(92.5% 0.084 155.995);\n    --color-green-300: oklch(87.1% 0.15 154.449);\n    --color-green-400: oklch(79.2% 0.209 151.711);\n    --color-green-500: oklch(72.3% 0.219 149.579);\n    --color-green-600: oklch(62.7% 0.194 149.214);\n    --color-green-700: oklch(52.7% 0.154 150.069);\n    --color-green-800: oklch(44.8% 0.119 151.328);\n    --color-green-900: oklch(39.3% 0.095 152.535);\n    --color-emerald-400: oklch(76.5% 0.177 163.223);\n    --color-emerald-500: oklch(69.6% 0.17 162.48);\n    --color-cyan-500: oklch(71.5% 0.143 215.221);\n    --color-sky-500: oklch(68.5% 0.169 237.323);\n    --color-blue-50: oklch(97% 0.014 254.604);\n    --color-blue-100: oklch(93.2% 0.032 255.585);\n    --color-blue-200: oklch(88.2% 0.059 254.128);\n    --color-blue-400: oklch(70.7% 0.165 254.624);\n    --color-blue-500: oklch(62.3% 0.214 259.815);\n    --color-blue-600: oklch(54.6% 0.245 262.881);\n    --color-blue-700: oklch(48.8% 0.243 264.376);\n    --color-blue-800: oklch(42.4% 0.199 265.638);\n    --color-blue-900: oklch(37.9% 0.146 265.522);\n    --color-indigo-50: oklch(96.2% 0.018 272.314);\n    --color-indigo-400: oklch(67.3% 0.182 276.935);\n    --color-indigo-500: oklch(58.5% 0.233 277.117);\n    --color-indigo-600: oklch(51.1% 0.262 276.966);\n    --color-violet-200: oklch(89.4% 0.057 293.283);\n    --color-violet-500: oklch(60.6% 0.25 292.717);\n    --color-purple-50: oklch(97.7% 0.014 308.299);\n    --color-purple-200: oklch(90.2% 0.063 306.703);\n    --color-purple-400: oklch(71.4% 0.203 305.504);\n    --color-purple-500: oklch(62.7% 0.265 303.9);\n    --color-purple-600: oklch(55.8% 0.288 302.321);\n    --color-purple-700: oklch(49.6% 0.265 301.924);\n    --color-pink-500: oklch(65.6% 0.241 354.308);\n    --color-slate-300: oklch(86.9% 0.022 252.894);\n    --color-slate-400: oklch(70.4% 0.04 256.788);\n    --color-slate-600: oklch(44.6% 0.043 257.281);\n    --color-slate-700: oklch(37.2% 0.044 257.287);\n    --color-slate-800: oklch(27.9% 0.041 260.031);\n    --color-slate-900: oklch(20.8% 0.042 265.755);\n    --color-slate-950: oklch(12.9% 0.042 264.695);\n    --color-gray-100: oklch(96.7% 0.003 264.542);\n    --color-gray-200: oklch(92.8% 0.006 264.531);\n    --color-gray-300: oklch(87.2% 0.01 258.338);\n    --color-gray-400: oklch(70.7% 0.022 261.325);\n    --color-gray-500: oklch(55.1% 0.027 264.364);\n    --color-gray-600: oklch(44.6% 0.03 256.802);\n    --color-gray-700: oklch(37.3% 0.034 259.733);\n    --color-gray-800: oklch(27.8% 0.033 256.848);\n    --color-gray-900: oklch(21% 0.034 264.665);\n    --color-zinc-600: oklch(44.2% 0.017 285.786);\n    --color-zinc-800: oklch(27.4% 0.006 286.033);\n    --color-neutral-300: oklch(87% 0 0);\n    --color-neutral-600: oklch(43.9% 0 0);\n    --color-neutral-900: oklch(20.5% 0 0);\n    --color-neutral-950: oklch(14.5% 0 0);\n    --color-black: #000;\n    --color-white: #fff;\n    --spacing: 0.25rem;\n    --breakpoint-xl: 80rem;\n    --container-xs: 20rem;\n    --container-sm: 24rem;\n    --container-md: 28rem;\n    --container-lg: 32rem;\n    --container-xl: 36rem;\n    --container-2xl: 42rem;\n    --container-3xl: 48rem;\n    --container-4xl: 56rem;\n    --container-5xl: 64rem;\n    --container-6xl: 72rem;\n    --container-7xl: 80rem;\n    --text-xs: 0.75rem;\n    --text-xs--line-height: calc(1 / 0.75);\n    --text-sm: 0.875rem;\n    --text-sm--line-height: calc(1.25 / 0.875);\n    --text-base: 1rem;\n    --text-base--line-height: calc(1.5 / 1);\n    --text-lg: 1.125rem;\n    --text-lg--line-height: calc(1.75 / 1.125);\n    --text-xl: 1.25rem;\n    --text-xl--line-height: calc(1.75 / 1.25);\n    --text-2xl: 1.5rem;\n    --text-2xl--line-height: calc(2 / 1.5);\n    --text-3xl: 1.875rem;\n    --text-3xl--line-height: calc(2.25 / 1.875);\n    --text-4xl: 2.25rem;\n    --text-4xl--line-height: calc(2.5 / 2.25);\n    --text-5xl: 3rem;\n    --text-5xl--line-height: 1;\n    --text-6xl: 3.75rem;\n    --text-6xl--line-height: 1;\n    --text-7xl: 4.5rem;\n    --text-7xl--line-height: 1;\n    --font-weight-thin: 100;\n    --font-weight-extralight: 200;\n    --font-weight-light: 300;\n    --font-weight-normal: 400;\n    --font-weight-medium: 500;\n    --font-weight-semibold: 600;\n    --font-weight-bold: 700;\n    --tracking-tighter: -0.05em;\n    --tracking-tight: -0.025em;\n    --tracking-wider: 0.05em;\n    --tracking-widest: 0.1em;\n    --leading-tight: 1.25;\n    --leading-relaxed: 1.625;\n    --radius-xs: 0.125rem;\n    --radius-2xl: 1rem;\n    --ease-out: cubic-bezier(0, 0, 0.2, 1);\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\n    --animate-spin: spin 1s linear infinite;\n    --animate-ping: ping 1s cubic-bezier(0, 0, 0.2, 1) infinite;\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n    --blur-sm: 8px;\n    --blur-lg: 16px;\n    --blur-xl: 24px;\n    --blur-2xl: 40px;\n    --blur-3xl: 64px;\n    --aspect-video: 16 / 9;\n    --default-transition-duration: 150ms;\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    --default-font-family: var(--font-geist-sans);\n    --default-mono-font-family: var(--font-geist-mono);\n    --color-border: var(--border);\n  }\n}\n@layer base {\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n    border: 0 solid;\n  }\n  html, :host {\n    line-height: 1.5;\n    -webkit-text-size-adjust: 100%;\n    tab-size: 4;\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\");\n    font-feature-settings: var(--default-font-feature-settings, normal);\n    font-variation-settings: var(--default-font-variation-settings, normal);\n    -webkit-tap-highlight-color: transparent;\n  }\n  hr {\n    height: 0;\n    color: inherit;\n    border-top-width: 1px;\n  }\n  abbr:where([title]) {\n    -webkit-text-decoration: underline dotted;\n    text-decoration: underline dotted;\n  }\n  h1, h2, h3, h4, h5, h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n  a {\n    color: inherit;\n    -webkit-text-decoration: inherit;\n    text-decoration: inherit;\n  }\n  b, strong {\n    font-weight: bolder;\n  }\n  code, kbd, samp, pre {\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace);\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\n    font-size: 1em;\n  }\n  small {\n    font-size: 80%;\n  }\n  sub, sup {\n    font-size: 75%;\n    line-height: 0;\n    position: relative;\n    vertical-align: baseline;\n  }\n  sub {\n    bottom: -0.25em;\n  }\n  sup {\n    top: -0.5em;\n  }\n  table {\n    text-indent: 0;\n    border-color: inherit;\n    border-collapse: collapse;\n  }\n  :-moz-focusring {\n    outline: auto;\n  }\n  progress {\n    vertical-align: baseline;\n  }\n  summary {\n    display: list-item;\n  }\n  ol, ul, menu {\n    list-style: none;\n  }\n  img, svg, video, canvas, audio, iframe, embed, object {\n    display: block;\n    vertical-align: middle;\n  }\n  img, video {\n    max-width: 100%;\n    height: auto;\n  }\n  button, input, select, optgroup, textarea, ::file-selector-button {\n    font: inherit;\n    font-feature-settings: inherit;\n    font-variation-settings: inherit;\n    letter-spacing: inherit;\n    color: inherit;\n    border-radius: 0;\n    background-color: transparent;\n    opacity: 1;\n  }\n  :where(select:is([multiple], [size])) optgroup {\n    font-weight: bolder;\n  }\n  :where(select:is([multiple], [size])) optgroup option {\n    padding-inline-start: 20px;\n  }\n  ::file-selector-button {\n    margin-inline-end: 4px;\n  }\n  ::placeholder {\n    opacity: 1;\n  }\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\n    ::placeholder {\n      color: currentcolor;\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, currentcolor 50%, transparent);\n      }\n    }\n  }\n  textarea {\n    resize: vertical;\n  }\n  ::-webkit-search-decoration {\n    -webkit-appearance: none;\n  }\n  ::-webkit-date-and-time-value {\n    min-height: 1lh;\n    text-align: inherit;\n  }\n  ::-webkit-datetime-edit {\n    display: inline-flex;\n  }\n  ::-webkit-datetime-edit-fields-wrapper {\n    padding: 0;\n  }\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\n    padding-block: 0;\n  }\n  :-moz-ui-invalid {\n    box-shadow: none;\n  }\n  button, input:where([type=\"button\"], [type=\"reset\"], [type=\"submit\"]), ::file-selector-button {\n    appearance: button;\n  }\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\n    height: auto;\n  }\n  [hidden]:where(:not([hidden=\"until-found\"])) {\n    display: none !important;\n  }\n}\n@layer utilities {\n  .\\@container\\/card {\n    container-type: inline-size;\n    container-name: card;\n  }\n  .\\@container\\/card-header {\n    container-type: inline-size;\n    container-name: card-header;\n  }\n  .\\@container\\/main {\n    container-type: inline-size;\n    container-name: main;\n  }\n  .pointer-events-none {\n    pointer-events: none;\n  }\n  .visible {\n    visibility: visible;\n  }\n  .sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border-width: 0;\n  }\n  .absolute {\n    position: absolute;\n  }\n  .fixed {\n    position: fixed;\n  }\n  .relative {\n    position: relative;\n  }\n  .static {\n    position: static;\n  }\n  .-inset-1 {\n    inset: calc(var(--spacing) * -1);\n  }\n  .inset-0 {\n    inset: calc(var(--spacing) * 0);\n  }\n  .inset-x-0 {\n    inset-inline: calc(var(--spacing) * 0);\n  }\n  .inset-y-0 {\n    inset-block: calc(var(--spacing) * 0);\n  }\n  .-top-10 {\n    top: calc(var(--spacing) * -10);\n  }\n  .-top-20 {\n    top: calc(var(--spacing) * -20);\n  }\n  .-top-40 {\n    top: calc(var(--spacing) * -40);\n  }\n  .top-0 {\n    top: calc(var(--spacing) * 0);\n  }\n  .top-1 {\n    top: calc(var(--spacing) * 1);\n  }\n  .top-1\\.5 {\n    top: calc(var(--spacing) * 1.5);\n  }\n  .top-1\\/2 {\n    top: calc(1/2 * 100%);\n  }\n  .top-2 {\n    top: calc(var(--spacing) * 2);\n  }\n  .top-3 {\n    top: calc(var(--spacing) * 3);\n  }\n  .top-3\\.5 {\n    top: calc(var(--spacing) * 3.5);\n  }\n  .top-4 {\n    top: calc(var(--spacing) * 4);\n  }\n  .top-6 {\n    top: calc(var(--spacing) * 6);\n  }\n  .top-8 {\n    top: calc(var(--spacing) * 8);\n  }\n  .top-12 {\n    top: calc(var(--spacing) * 12);\n  }\n  .top-16 {\n    top: calc(var(--spacing) * 16);\n  }\n  .top-20 {\n    top: calc(var(--spacing) * 20);\n  }\n  .top-24 {\n    top: calc(var(--spacing) * 24);\n  }\n  .top-\\[50\\%\\] {\n    top: 50%;\n  }\n  .top-\\[360px\\] {\n    top: 360px;\n  }\n  .-right-8 {\n    right: calc(var(--spacing) * -8);\n  }\n  .-right-20 {\n    right: calc(var(--spacing) * -20);\n  }\n  .right-0 {\n    right: calc(var(--spacing) * 0);\n  }\n  .right-1 {\n    right: calc(var(--spacing) * 1);\n  }\n  .right-2 {\n    right: calc(var(--spacing) * 2);\n  }\n  .right-3 {\n    right: calc(var(--spacing) * 3);\n  }\n  .right-4 {\n    right: calc(var(--spacing) * 4);\n  }\n  .right-6 {\n    right: calc(var(--spacing) * 6);\n  }\n  .right-8 {\n    right: calc(var(--spacing) * 8);\n  }\n  .right-10 {\n    right: calc(var(--spacing) * 10);\n  }\n  .-bottom-4 {\n    bottom: calc(var(--spacing) * -4);\n  }\n  .bottom-0 {\n    bottom: calc(var(--spacing) * 0);\n  }\n  .bottom-2 {\n    bottom: calc(var(--spacing) * 2);\n  }\n  .bottom-3 {\n    bottom: calc(var(--spacing) * 3);\n  }\n  .bottom-6 {\n    bottom: calc(var(--spacing) * 6);\n  }\n  .bottom-8 {\n    bottom: calc(var(--spacing) * 8);\n  }\n  .bottom-10 {\n    bottom: calc(var(--spacing) * 10);\n  }\n  .bottom-20 {\n    bottom: calc(var(--spacing) * 20);\n  }\n  .-left-4 {\n    left: calc(var(--spacing) * -4);\n  }\n  .-left-8 {\n    left: calc(var(--spacing) * -8);\n  }\n  .-left-20 {\n    left: calc(var(--spacing) * -20);\n  }\n  .left-0 {\n    left: calc(var(--spacing) * 0);\n  }\n  .left-1\\/2 {\n    left: calc(1/2 * 100%);\n  }\n  .left-2 {\n    left: calc(var(--spacing) * 2);\n  }\n  .left-3 {\n    left: calc(var(--spacing) * 3);\n  }\n  .left-6 {\n    left: calc(var(--spacing) * 6);\n  }\n  .left-8 {\n    left: calc(var(--spacing) * 8);\n  }\n  .left-10 {\n    left: calc(var(--spacing) * 10);\n  }\n  .left-\\[50\\%\\] {\n    left: 50%;\n  }\n  .isolate {\n    isolation: isolate;\n  }\n  .-z-10 {\n    z-index: calc(10 * -1);\n  }\n  .z-0 {\n    z-index: 0;\n  }\n  .z-10 {\n    z-index: 10;\n  }\n  .z-20 {\n    z-index: 20;\n  }\n  .z-50 {\n    z-index: 50;\n  }\n  .z-\\[60\\] {\n    z-index: 60;\n  }\n  .z-\\[150\\] {\n    z-index: 150;\n  }\n  .col-span-5 {\n    grid-column: span 5 / span 5;\n  }\n  .col-start-2 {\n    grid-column-start: 2;\n  }\n  .row-span-2 {\n    grid-row: span 2 / span 2;\n  }\n  .row-start-1 {\n    grid-row-start: 1;\n  }\n  .container {\n    width: 100%;\n    @media (width >= 40rem) {\n      max-width: 40rem;\n    }\n    @media (width >= 48rem) {\n      max-width: 48rem;\n    }\n    @media (width >= 64rem) {\n      max-width: 64rem;\n    }\n    @media (width >= 80rem) {\n      max-width: 80rem;\n    }\n    @media (width >= 96rem) {\n      max-width: 96rem;\n    }\n  }\n  .-mx-1 {\n    margin-inline: calc(var(--spacing) * -1);\n  }\n  .mx-1 {\n    margin-inline: calc(var(--spacing) * 1);\n  }\n  .mx-2 {\n    margin-inline: calc(var(--spacing) * 2);\n  }\n  .mx-3\\.5 {\n    margin-inline: calc(var(--spacing) * 3.5);\n  }\n  .mx-4 {\n    margin-inline: calc(var(--spacing) * 4);\n  }\n  .mx-auto {\n    margin-inline: auto;\n  }\n  .my-0\\.5 {\n    margin-block: calc(var(--spacing) * 0.5);\n  }\n  .my-1 {\n    margin-block: calc(var(--spacing) * 1);\n  }\n  .my-auto {\n    margin-block: auto;\n  }\n  .-mt-0\\.5 {\n    margin-top: calc(var(--spacing) * -0.5);\n  }\n  .-mt-4 {\n    margin-top: calc(var(--spacing) * -4);\n  }\n  .-mt-\\[2\\.5rem\\] {\n    margin-top: calc(2.5rem * -1);\n  }\n  .mt-0\\.5 {\n    margin-top: calc(var(--spacing) * 0.5);\n  }\n  .mt-1 {\n    margin-top: calc(var(--spacing) * 1);\n  }\n  .mt-2 {\n    margin-top: calc(var(--spacing) * 2);\n  }\n  .mt-3 {\n    margin-top: calc(var(--spacing) * 3);\n  }\n  .mt-4 {\n    margin-top: calc(var(--spacing) * 4);\n  }\n  .mt-5 {\n    margin-top: calc(var(--spacing) * 5);\n  }\n  .mt-6 {\n    margin-top: calc(var(--spacing) * 6);\n  }\n  .mt-8 {\n    margin-top: calc(var(--spacing) * 8);\n  }\n  .mt-10 {\n    margin-top: calc(var(--spacing) * 10);\n  }\n  .mt-12 {\n    margin-top: calc(var(--spacing) * 12);\n  }\n  .mt-16 {\n    margin-top: calc(var(--spacing) * 16);\n  }\n  .mt-auto {\n    margin-top: auto;\n  }\n  .mr-1 {\n    margin-right: calc(var(--spacing) * 1);\n  }\n  .mr-2 {\n    margin-right: calc(var(--spacing) * 2);\n  }\n  .mr-3 {\n    margin-right: calc(var(--spacing) * 3);\n  }\n  .mr-4 {\n    margin-right: calc(var(--spacing) * 4);\n  }\n  .mb-1 {\n    margin-bottom: calc(var(--spacing) * 1);\n  }\n  .mb-2 {\n    margin-bottom: calc(var(--spacing) * 2);\n  }\n  .mb-3 {\n    margin-bottom: calc(var(--spacing) * 3);\n  }\n  .mb-4 {\n    margin-bottom: calc(var(--spacing) * 4);\n  }\n  .mb-6 {\n    margin-bottom: calc(var(--spacing) * 6);\n  }\n  .mb-8 {\n    margin-bottom: calc(var(--spacing) * 8);\n  }\n  .mb-10 {\n    margin-bottom: calc(var(--spacing) * 10);\n  }\n  .mb-12 {\n    margin-bottom: calc(var(--spacing) * 12);\n  }\n  .-ml-1 {\n    margin-left: calc(var(--spacing) * -1);\n  }\n  .ml-0\\.5 {\n    margin-left: calc(var(--spacing) * 0.5);\n  }\n  .ml-1 {\n    margin-left: calc(var(--spacing) * 1);\n  }\n  .ml-2 {\n    margin-left: calc(var(--spacing) * 2);\n  }\n  .ml-3 {\n    margin-left: calc(var(--spacing) * 3);\n  }\n  .ml-4 {\n    margin-left: calc(var(--spacing) * 4);\n  }\n  .ml-auto {\n    margin-left: auto;\n  }\n  .line-clamp-1 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 1;\n  }\n  .line-clamp-2 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 2;\n  }\n  .block {\n    display: block;\n  }\n  .flex {\n    display: flex;\n  }\n  .grid {\n    display: grid;\n  }\n  .hidden {\n    display: none;\n  }\n  .inline-block {\n    display: inline-block;\n  }\n  .inline-flex {\n    display: inline-flex;\n  }\n  .table {\n    display: table;\n  }\n  .table-caption {\n    display: table-caption;\n  }\n  .table-cell {\n    display: table-cell;\n  }\n  .table-row {\n    display: table-row;\n  }\n  .field-sizing-content {\n    field-sizing: content;\n  }\n  .aspect-square {\n    aspect-ratio: 1 / 1;\n  }\n  .aspect-video {\n    aspect-ratio: var(--aspect-video);\n  }\n  .\\!size-7 {\n    width: calc(var(--spacing) * 7) !important;\n    height: calc(var(--spacing) * 7) !important;\n  }\n  .size-2 {\n    width: calc(var(--spacing) * 2);\n    height: calc(var(--spacing) * 2);\n  }\n  .size-2\\.5 {\n    width: calc(var(--spacing) * 2.5);\n    height: calc(var(--spacing) * 2.5);\n  }\n  .size-3\\.5 {\n    width: calc(var(--spacing) * 3.5);\n    height: calc(var(--spacing) * 3.5);\n  }\n  .size-4 {\n    width: calc(var(--spacing) * 4);\n    height: calc(var(--spacing) * 4);\n  }\n  .size-5 {\n    width: calc(var(--spacing) * 5);\n    height: calc(var(--spacing) * 5);\n  }\n  .size-6 {\n    width: calc(var(--spacing) * 6);\n    height: calc(var(--spacing) * 6);\n  }\n  .size-7 {\n    width: calc(var(--spacing) * 7);\n    height: calc(var(--spacing) * 7);\n  }\n  .size-8 {\n    width: calc(var(--spacing) * 8);\n    height: calc(var(--spacing) * 8);\n  }\n  .size-9 {\n    width: calc(var(--spacing) * 9);\n    height: calc(var(--spacing) * 9);\n  }\n  .size-10 {\n    width: calc(var(--spacing) * 10);\n    height: calc(var(--spacing) * 10);\n  }\n  .size-full {\n    width: 100%;\n    height: 100%;\n  }\n  .h-\\(--header-height\\) {\n    height: var(--header-height);\n  }\n  .h-0\\.5 {\n    height: calc(var(--spacing) * 0.5);\n  }\n  .h-1 {\n    height: calc(var(--spacing) * 1);\n  }\n  .h-1\\/4 {\n    height: calc(1/4 * 100%);\n  }\n  .h-2 {\n    height: calc(var(--spacing) * 2);\n  }\n  .h-2\\.5 {\n    height: calc(var(--spacing) * 2.5);\n  }\n  .h-3 {\n    height: calc(var(--spacing) * 3);\n  }\n  .h-4 {\n    height: calc(var(--spacing) * 4);\n  }\n  .h-5 {\n    height: calc(var(--spacing) * 5);\n  }\n  .h-6 {\n    height: calc(var(--spacing) * 6);\n  }\n  .h-7 {\n    height: calc(var(--spacing) * 7);\n  }\n  .h-8 {\n    height: calc(var(--spacing) * 8);\n  }\n  .h-9 {\n    height: calc(var(--spacing) * 9);\n  }\n  .h-10 {\n    height: calc(var(--spacing) * 10);\n  }\n  .h-11 {\n    height: calc(var(--spacing) * 11);\n  }\n  .h-12 {\n    height: calc(var(--spacing) * 12);\n  }\n  .h-16 {\n    height: calc(var(--spacing) * 16);\n  }\n  .h-20 {\n    height: calc(var(--spacing) * 20);\n  }\n  .h-24 {\n    height: calc(var(--spacing) * 24);\n  }\n  .h-32 {\n    height: calc(var(--spacing) * 32);\n  }\n  .h-44 {\n    height: calc(var(--spacing) * 44);\n  }\n  .h-48 {\n    height: calc(var(--spacing) * 48);\n  }\n  .h-56 {\n    height: calc(var(--spacing) * 56);\n  }\n  .h-64 {\n    height: calc(var(--spacing) * 64);\n  }\n  .h-80 {\n    height: calc(var(--spacing) * 80);\n  }\n  .h-96 {\n    height: calc(var(--spacing) * 96);\n  }\n  .h-\\[1\\.15rem\\] {\n    height: 1.15rem;\n  }\n  .h-\\[250px\\] {\n    height: 250px;\n  }\n  .h-\\[400px\\] {\n    height: 400px;\n  }\n  .h-\\[500px\\] {\n    height: 500px;\n  }\n  .h-\\[600px\\] {\n    height: 600px;\n  }\n  .h-\\[calc\\(100\\%-1px\\)\\] {\n    height: calc(100% - 1px);\n  }\n  .h-\\[var\\(--radix-select-trigger-height\\)\\] {\n    height: var(--radix-select-trigger-height);\n  }\n  .h-auto {\n    height: auto;\n  }\n  .h-fit {\n    height: fit-content;\n  }\n  .h-full {\n    height: 100%;\n  }\n  .h-px {\n    height: 1px;\n  }\n  .h-screen {\n    height: 100vh;\n  }\n  .h-svh {\n    height: 100svh;\n  }\n  .max-h-\\(--radix-dropdown-menu-content-available-height\\) {\n    max-height: var(--radix-dropdown-menu-content-available-height);\n  }\n  .max-h-\\(--radix-select-content-available-height\\) {\n    max-height: var(--radix-select-content-available-height);\n  }\n  .max-h-\\[80vh\\] {\n    max-height: 80vh;\n  }\n  .max-h-\\[600px\\] {\n    max-height: 600px;\n  }\n  .max-h-screen {\n    max-height: 100vh;\n  }\n  .min-h-0 {\n    min-height: calc(var(--spacing) * 0);\n  }\n  .min-h-16 {\n    min-height: calc(var(--spacing) * 16);\n  }\n  .min-h-screen {\n    min-height: 100vh;\n  }\n  .min-h-svh {\n    min-height: 100svh;\n  }\n  .w-\\(--radix-dropdown-menu-trigger-width\\) {\n    width: var(--radix-dropdown-menu-trigger-width);\n  }\n  .w-\\(--sidebar-width\\) {\n    width: var(--sidebar-width);\n  }\n  .w-0 {\n    width: calc(var(--spacing) * 0);\n  }\n  .w-1 {\n    width: calc(var(--spacing) * 1);\n  }\n  .w-1\\/2 {\n    width: calc(1/2 * 100%);\n  }\n  .w-2 {\n    width: calc(var(--spacing) * 2);\n  }\n  .w-2\\.5 {\n    width: calc(var(--spacing) * 2.5);\n  }\n  .w-3 {\n    width: calc(var(--spacing) * 3);\n  }\n  .w-3\\/4 {\n    width: calc(3/4 * 100%);\n  }\n  .w-4 {\n    width: calc(var(--spacing) * 4);\n  }\n  .w-5 {\n    width: calc(var(--spacing) * 5);\n  }\n  .w-6 {\n    width: calc(var(--spacing) * 6);\n  }\n  .w-7 {\n    width: calc(var(--spacing) * 7);\n  }\n  .w-8 {\n    width: calc(var(--spacing) * 8);\n  }\n  .w-10 {\n    width: calc(var(--spacing) * 10);\n  }\n  .w-12 {\n    width: calc(var(--spacing) * 12);\n  }\n  .w-16 {\n    width: calc(var(--spacing) * 16);\n  }\n  .w-20 {\n    width: calc(var(--spacing) * 20);\n  }\n  .w-24 {\n    width: calc(var(--spacing) * 24);\n  }\n  .w-32 {\n    width: calc(var(--spacing) * 32);\n  }\n  .w-48 {\n    width: calc(var(--spacing) * 48);\n  }\n  .w-56 {\n    width: calc(var(--spacing) * 56);\n  }\n  .w-64 {\n    width: calc(var(--spacing) * 64);\n  }\n  .w-80 {\n    width: calc(var(--spacing) * 80);\n  }\n  .w-96 {\n    width: calc(var(--spacing) * 96);\n  }\n  .w-\\[100px\\] {\n    width: 100px;\n  }\n  .w-\\[140px\\] {\n    width: 140px;\n  }\n  .w-\\[160px\\] {\n    width: 160px;\n  }\n  .w-\\[300px\\] {\n    width: 300px;\n  }\n  .w-\\[400px\\] {\n    width: 400px;\n  }\n  .w-\\[500px\\] {\n    width: 500px;\n  }\n  .w-\\[600px\\] {\n    width: 600px;\n  }\n  .w-auto {\n    width: auto;\n  }\n  .w-fit {\n    width: fit-content;\n  }\n  .w-full {\n    width: 100%;\n  }\n  .max-w-\\(--breakpoint-xl\\) {\n    max-width: var(--breakpoint-xl);\n  }\n  .max-w-\\(--skeleton-width\\) {\n    max-width: var(--skeleton-width);\n  }\n  .max-w-2xl {\n    max-width: var(--container-2xl);\n  }\n  .max-w-3xl {\n    max-width: var(--container-3xl);\n  }\n  .max-w-4xl {\n    max-width: var(--container-4xl);\n  }\n  .max-w-5xl {\n    max-width: var(--container-5xl);\n  }\n  .max-w-6xl {\n    max-width: var(--container-6xl);\n  }\n  .max-w-7xl {\n    max-width: var(--container-7xl);\n  }\n  .max-w-\\[90\\%\\] {\n    max-width: 90%;\n  }\n  .max-w-\\[calc\\(100\\%-2rem\\)\\] {\n    max-width: calc(100% - 2rem);\n  }\n  .max-w-\\[calc\\(100vw-2rem\\)\\] {\n    max-width: calc(100vw - 2rem);\n  }\n  .max-w-fit {\n    max-width: fit-content;\n  }\n  .max-w-full {\n    max-width: 100%;\n  }\n  .max-w-lg {\n    max-width: var(--container-lg);\n  }\n  .max-w-md {\n    max-width: var(--container-md);\n  }\n  .max-w-screen-xl {\n    max-width: var(--breakpoint-xl);\n  }\n  .max-w-xl {\n    max-width: var(--container-xl);\n  }\n  .max-w-xs {\n    max-width: var(--container-xs);\n  }\n  .min-w-0 {\n    min-width: calc(var(--spacing) * 0);\n  }\n  .min-w-5 {\n    min-width: calc(var(--spacing) * 5);\n  }\n  .min-w-8 {\n    min-width: calc(var(--spacing) * 8);\n  }\n  .min-w-9 {\n    min-width: calc(var(--spacing) * 9);\n  }\n  .min-w-10 {\n    min-width: calc(var(--spacing) * 10);\n  }\n  .min-w-24 {\n    min-width: calc(var(--spacing) * 24);\n  }\n  .min-w-56 {\n    min-width: calc(var(--spacing) * 56);\n  }\n  .min-w-\\[8rem\\] {\n    min-width: 8rem;\n  }\n  .min-w-\\[300px\\] {\n    min-width: 300px;\n  }\n  .min-w-\\[var\\(--radix-select-trigger-width\\)\\] {\n    min-width: var(--radix-select-trigger-width);\n  }\n  .flex-1 {\n    flex: 1;\n  }\n  .flex-shrink-0 {\n    flex-shrink: 0;\n  }\n  .shrink-0 {\n    flex-shrink: 0;\n  }\n  .flex-grow {\n    flex-grow: 1;\n  }\n  .caption-bottom {\n    caption-side: bottom;\n  }\n  .origin-\\(--radix-dropdown-menu-content-transform-origin\\) {\n    transform-origin: var(--radix-dropdown-menu-content-transform-origin);\n  }\n  .origin-\\(--radix-select-content-transform-origin\\) {\n    transform-origin: var(--radix-select-content-transform-origin);\n  }\n  .origin-\\(--radix-tooltip-content-transform-origin\\) {\n    transform-origin: var(--radix-tooltip-content-transform-origin);\n  }\n  .-translate-x-1\\/2 {\n    --tw-translate-x: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-x-px {\n    --tw-translate-x: -1px;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-\\[-50\\%\\] {\n    --tw-translate-x: -50%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-px {\n    --tw-translate-x: 1px;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-y-1\\/2 {\n    --tw-translate-y: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-0\\.5 {\n    --tw-translate-y: calc(var(--spacing) * 0.5);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-\\[-50\\%\\] {\n    --tw-translate-y: -50%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-\\[calc\\(-50\\%_-_2px\\)\\] {\n    --tw-translate-y: calc(-50% - 2px);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .scale-90 {\n    --tw-scale-x: 90%;\n    --tw-scale-y: 90%;\n    --tw-scale-z: 90%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .-rotate-12 {\n    rotate: calc(12deg * -1);\n  }\n  .-rotate-90 {\n    rotate: calc(90deg * -1);\n  }\n  .rotate-6 {\n    rotate: 6deg;\n  }\n  .rotate-12 {\n    rotate: 12deg;\n  }\n  .rotate-45 {\n    rotate: 45deg;\n  }\n  .transform {\n    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n  }\n  .transform-gpu {\n    transform: translateZ(0) var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n  }\n  .animate-ping {\n    animation: var(--animate-ping);\n  }\n  .animate-pulse {\n    animation: var(--animate-pulse);\n  }\n  .animate-spin {\n    animation: var(--animate-spin);\n  }\n  .cursor-default {\n    cursor: default;\n  }\n  .cursor-not-allowed {\n    cursor: not-allowed;\n  }\n  .cursor-pointer {\n    cursor: pointer;\n  }\n  .touch-none {\n    touch-action: none;\n  }\n  .resize {\n    resize: both;\n  }\n  .resize-none {\n    resize: none;\n  }\n  .scroll-my-1 {\n    scroll-margin-block: calc(var(--spacing) * 1);\n  }\n  .list-disc {\n    list-style-type: disc;\n  }\n  .break-inside-avoid {\n    break-inside: avoid;\n  }\n  .auto-rows-min {\n    grid-auto-rows: min-content;\n  }\n  .grid-cols-1 {\n    grid-template-columns: repeat(1, minmax(0, 1fr));\n  }\n  .grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n  .grid-cols-3 {\n    grid-template-columns: repeat(3, minmax(0, 1fr));\n  }\n  .grid-rows-\\[auto_auto\\] {\n    grid-template-rows: auto auto;\n  }\n  .flex-col {\n    flex-direction: column;\n  }\n  .flex-col-reverse {\n    flex-direction: column-reverse;\n  }\n  .flex-row {\n    flex-direction: row;\n  }\n  .flex-wrap {\n    flex-wrap: wrap;\n  }\n  .items-center {\n    align-items: center;\n  }\n  .items-end {\n    align-items: flex-end;\n  }\n  .items-start {\n    align-items: flex-start;\n  }\n  .items-stretch {\n    align-items: stretch;\n  }\n  .justify-around {\n    justify-content: space-around;\n  }\n  .justify-between {\n    justify-content: space-between;\n  }\n  .justify-center {\n    justify-content: center;\n  }\n  .justify-end {\n    justify-content: flex-end;\n  }\n  .justify-start {\n    justify-content: flex-start;\n  }\n  .\\[gap\\:var\\(--gap\\)\\] {\n    gap: var(--gap);\n  }\n  .gap-0\\.5 {\n    gap: calc(var(--spacing) * 0.5);\n  }\n  .gap-1 {\n    gap: calc(var(--spacing) * 1);\n  }\n  .gap-1\\.5 {\n    gap: calc(var(--spacing) * 1.5);\n  }\n  .gap-2 {\n    gap: calc(var(--spacing) * 2);\n  }\n  .gap-3 {\n    gap: calc(var(--spacing) * 3);\n  }\n  .gap-4 {\n    gap: calc(var(--spacing) * 4);\n  }\n  .gap-5 {\n    gap: calc(var(--spacing) * 5);\n  }\n  .gap-6 {\n    gap: calc(var(--spacing) * 6);\n  }\n  .gap-8 {\n    gap: calc(var(--spacing) * 8);\n  }\n  .gap-12 {\n    gap: calc(var(--spacing) * 12);\n  }\n  .space-y-0\\.5 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 0.5) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 0.5) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 3) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-5 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 5) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 5) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-6 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-8 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .-space-x-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * -2) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * -2) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-0\\.5 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 0.5) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 0.5) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-3 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 3) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 3) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .space-x-6 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 6) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .place-self-end {\n    place-self: end;\n  }\n  .self-start {\n    align-self: flex-start;\n  }\n  .justify-self-end {\n    justify-self: flex-end;\n  }\n  .truncate {\n    overflow: hidden;\n    text-overflow: ellipsis;\n    white-space: nowrap;\n  }\n  .overflow-auto {\n    overflow: auto;\n  }\n  .overflow-hidden {\n    overflow: hidden;\n  }\n  .overflow-x-auto {\n    overflow-x: auto;\n  }\n  .overflow-x-hidden {\n    overflow-x: hidden;\n  }\n  .overflow-y-auto {\n    overflow-y: auto;\n  }\n  .rounded {\n    border-radius: 0.25rem;\n  }\n  .rounded-2xl {\n    border-radius: var(--radius-2xl);\n  }\n  .rounded-\\[2px\\] {\n    border-radius: 2px;\n  }\n  .rounded-\\[4px\\] {\n    border-radius: 4px;\n  }\n  .rounded-\\[inherit\\] {\n    border-radius: inherit;\n  }\n  .rounded-full {\n    border-radius: calc(infinity * 1px);\n  }\n  .rounded-lg {\n    border-radius: var(--radius);\n  }\n  .rounded-md {\n    border-radius: calc(var(--radius) - 2px);\n  }\n  .rounded-none {\n    border-radius: 0;\n  }\n  .rounded-sm {\n    border-radius: calc(var(--radius) - 4px);\n  }\n  .rounded-xl {\n    border-radius: calc(var(--radius) + 4px);\n  }\n  .rounded-xs {\n    border-radius: var(--radius-xs);\n  }\n  .rounded-t-xl {\n    border-top-left-radius: calc(var(--radius) + 4px);\n    border-top-right-radius: calc(var(--radius) + 4px);\n  }\n  .border {\n    border-style: var(--tw-border-style);\n    border-width: 1px;\n  }\n  .border-0 {\n    border-style: var(--tw-border-style);\n    border-width: 0px;\n  }\n  .border-2 {\n    border-style: var(--tw-border-style);\n    border-width: 2px;\n  }\n  .border-4 {\n    border-style: var(--tw-border-style);\n    border-width: 4px;\n  }\n  .border-\\[1\\.5px\\] {\n    border-style: var(--tw-border-style);\n    border-width: 1.5px;\n  }\n  .border-t {\n    border-top-style: var(--tw-border-style);\n    border-top-width: 1px;\n  }\n  .border-r {\n    border-right-style: var(--tw-border-style);\n    border-right-width: 1px;\n  }\n  .border-b {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 1px;\n  }\n  .border-l {\n    border-left-style: var(--tw-border-style);\n    border-left-width: 1px;\n  }\n  .border-dashed {\n    --tw-border-style: dashed;\n    border-style: dashed;\n  }\n  .border-\\(--color-border\\) {\n    border-color: var(--color-border);\n  }\n  .border-blue-200 {\n    border-color: var(--color-blue-200);\n  }\n  .border-blue-500\\/10 {\n    border-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-blue-500) 10%, transparent);\n    }\n  }\n  .border-border {\n    border-color: var(--border);\n  }\n  .border-border\\/30 {\n    border-color: var(--border);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--border) 30%, transparent);\n    }\n  }\n  .border-border\\/50 {\n    border-color: var(--border);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--border) 50%, transparent);\n    }\n  }\n  .border-destructive\\/20 {\n    border-color: var(--destructive);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n    }\n  }\n  .border-emerald-500\\/20 {\n    border-color: color-mix(in srgb, oklch(69.6% 0.17 162.48) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-emerald-500) 20%, transparent);\n    }\n  }\n  .border-gray-100 {\n    border-color: var(--color-gray-100);\n  }\n  .border-gray-200 {\n    border-color: var(--color-gray-200);\n  }\n  .border-gray-900 {\n    border-color: var(--color-gray-900);\n  }\n  .border-green-200 {\n    border-color: var(--color-green-200);\n  }\n  .border-green-400\\/50 {\n    border-color: color-mix(in srgb, oklch(79.2% 0.209 151.711) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-green-400) 50%, transparent);\n    }\n  }\n  .border-green-500\\/20 {\n    border-color: color-mix(in srgb, oklch(72.3% 0.219 149.579) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-green-500) 20%, transparent);\n    }\n  }\n  .border-indigo-400\\/30 {\n    border-color: color-mix(in srgb, oklch(67.3% 0.182 276.935) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-indigo-400) 30%, transparent);\n    }\n  }\n  .border-input {\n    border-color: var(--input);\n  }\n  .border-muted-foreground\\/30 {\n    border-color: var(--muted-foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--muted-foreground) 30%, transparent);\n    }\n  }\n  .border-muted\\/20 {\n    border-color: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--muted) 20%, transparent);\n    }\n  }\n  .border-muted\\/30 {\n    border-color: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--muted) 30%, transparent);\n    }\n  }\n  .border-primary {\n    border-color: var(--primary);\n  }\n  .border-primary\\/10 {\n    border-color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--primary) 10%, transparent);\n    }\n  }\n  .border-primary\\/20 {\n    border-color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--primary) 20%, transparent);\n    }\n  }\n  .border-primary\\/50 {\n    border-color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--primary) 50%, transparent);\n    }\n  }\n  .border-red-200 {\n    border-color: var(--color-red-200);\n  }\n  .border-red-500 {\n    border-color: var(--color-red-500);\n  }\n  .border-sidebar-border {\n    border-color: var(--sidebar-border);\n  }\n  .border-slate-600\\/30 {\n    border-color: color-mix(in srgb, oklch(44.6% 0.043 257.281) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-slate-600) 30%, transparent);\n    }\n  }\n  .border-slate-700\\/50 {\n    border-color: color-mix(in srgb, oklch(37.2% 0.044 257.287) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-slate-700) 50%, transparent);\n    }\n  }\n  .border-transparent {\n    border-color: transparent;\n  }\n  .border-white\\/10 {\n    border-color: color-mix(in srgb, #fff 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-white) 10%, transparent);\n    }\n  }\n  .border-white\\/50 {\n    border-color: color-mix(in srgb, #fff 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-white) 50%, transparent);\n    }\n  }\n  .border-yellow-200 {\n    border-color: var(--color-yellow-200);\n  }\n  .border-t-transparent {\n    border-top-color: transparent;\n  }\n  .border-l-transparent {\n    border-left-color: transparent;\n  }\n  .bg-\\(--color-bg\\) {\n    background-color: var(--color-bg);\n  }\n  .bg-accent {\n    background-color: var(--accent);\n  }\n  .bg-background {\n    background-color: var(--background);\n  }\n  .bg-background\\/80 {\n    background-color: var(--background);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--background) 80%, transparent);\n    }\n  }\n  .bg-black {\n    background-color: var(--color-black);\n  }\n  .bg-black\\/10 {\n    background-color: color-mix(in srgb, #000 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 10%, transparent);\n    }\n  }\n  .bg-black\\/20 {\n    background-color: color-mix(in srgb, #000 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 20%, transparent);\n    }\n  }\n  .bg-black\\/50 {\n    background-color: color-mix(in srgb, #000 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);\n    }\n  }\n  .bg-black\\/60 {\n    background-color: color-mix(in srgb, #000 60%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 60%, transparent);\n    }\n  }\n  .bg-black\\/70 {\n    background-color: color-mix(in srgb, #000 70%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 70%, transparent);\n    }\n  }\n  .bg-black\\/80 {\n    background-color: color-mix(in srgb, #000 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 80%, transparent);\n    }\n  }\n  .bg-black\\/90 {\n    background-color: color-mix(in srgb, #000 90%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 90%, transparent);\n    }\n  }\n  .bg-blue-50 {\n    background-color: var(--color-blue-50);\n  }\n  .bg-blue-400 {\n    background-color: var(--color-blue-400);\n  }\n  .bg-blue-500 {\n    background-color: var(--color-blue-500);\n  }\n  .bg-blue-500\\/20 {\n    background-color: color-mix(in srgb, oklch(62.3% 0.214 259.815) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-blue-500) 20%, transparent);\n    }\n  }\n  .bg-border {\n    background-color: var(--border);\n  }\n  .bg-card {\n    background-color: var(--card);\n  }\n  .bg-card\\/50 {\n    background-color: var(--card);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--card) 50%, transparent);\n    }\n  }\n  .bg-destructive {\n    background-color: var(--destructive);\n  }\n  .bg-destructive\\/20 {\n    background-color: var(--destructive);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n    }\n  }\n  .bg-foreground\\/30 {\n    background-color: var(--foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--foreground) 30%, transparent);\n    }\n  }\n  .bg-gray-100 {\n    background-color: var(--color-gray-100);\n  }\n  .bg-gray-400\\/5 {\n    background-color: color-mix(in srgb, oklch(70.7% 0.022 261.325) 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-400) 5%, transparent);\n    }\n  }\n  .bg-gray-900 {\n    background-color: var(--color-gray-900);\n  }\n  .bg-green-50 {\n    background-color: var(--color-green-50);\n  }\n  .bg-green-100 {\n    background-color: var(--color-green-100);\n  }\n  .bg-green-100\\/70 {\n    background-color: color-mix(in srgb, oklch(96.2% 0.044 156.743) 70%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-green-100) 70%, transparent);\n    }\n  }\n  .bg-green-500 {\n    background-color: var(--color-green-500);\n  }\n  .bg-green-500\\/20 {\n    background-color: color-mix(in srgb, oklch(72.3% 0.219 149.579) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-green-500) 20%, transparent);\n    }\n  }\n  .bg-green-600 {\n    background-color: var(--color-green-600);\n  }\n  .bg-indigo-50 {\n    background-color: var(--color-indigo-50);\n  }\n  .bg-muted {\n    background-color: var(--muted);\n  }\n  .bg-muted\\/10 {\n    background-color: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--muted) 10%, transparent);\n    }\n  }\n  .bg-muted\\/20 {\n    background-color: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--muted) 20%, transparent);\n    }\n  }\n  .bg-muted\\/30 {\n    background-color: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--muted) 30%, transparent);\n    }\n  }\n  .bg-muted\\/50 {\n    background-color: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--muted) 50%, transparent);\n    }\n  }\n  .bg-orange-50 {\n    background-color: var(--color-orange-50);\n  }\n  .bg-orange-400 {\n    background-color: var(--color-orange-400);\n  }\n  .bg-popover {\n    background-color: var(--popover);\n  }\n  .bg-primary {\n    background-color: var(--primary);\n  }\n  .bg-primary\\/5 {\n    background-color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--primary) 5%, transparent);\n    }\n  }\n  .bg-primary\\/10 {\n    background-color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--primary) 10%, transparent);\n    }\n  }\n  .bg-primary\\/20 {\n    background-color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--primary) 20%, transparent);\n    }\n  }\n  .bg-purple-50 {\n    background-color: var(--color-purple-50);\n  }\n  .bg-purple-400 {\n    background-color: var(--color-purple-400);\n  }\n  .bg-red-50 {\n    background-color: var(--color-red-50);\n  }\n  .bg-red-100 {\n    background-color: var(--color-red-100);\n  }\n  .bg-red-100\\/70 {\n    background-color: color-mix(in srgb, oklch(93.6% 0.032 17.717) 70%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-red-100) 70%, transparent);\n    }\n  }\n  .bg-red-500 {\n    background-color: var(--color-red-500);\n  }\n  .bg-secondary {\n    background-color: var(--secondary);\n  }\n  .bg-secondary\\/20 {\n    background-color: var(--secondary);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--secondary) 20%, transparent);\n    }\n  }\n  .bg-secondary\\/30 {\n    background-color: var(--secondary);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--secondary) 30%, transparent);\n    }\n  }\n  .bg-secondary\\/50 {\n    background-color: var(--secondary);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--secondary) 50%, transparent);\n    }\n  }\n  .bg-sidebar {\n    background-color: var(--sidebar);\n  }\n  .bg-sidebar-border {\n    background-color: var(--sidebar-border);\n  }\n  .bg-slate-700\\/50 {\n    background-color: color-mix(in srgb, oklch(37.2% 0.044 257.287) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-slate-700) 50%, transparent);\n    }\n  }\n  .bg-slate-800\\/50 {\n    background-color: color-mix(in srgb, oklch(27.9% 0.041 260.031) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-slate-800) 50%, transparent);\n    }\n  }\n  .bg-slate-950 {\n    background-color: var(--color-slate-950);\n  }\n  .bg-transparent {\n    background-color: transparent;\n  }\n  .bg-violet-500\\/5 {\n    background-color: color-mix(in srgb, oklch(60.6% 0.25 292.717) 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-violet-500) 5%, transparent);\n    }\n  }\n  .bg-violet-500\\/10 {\n    background-color: color-mix(in srgb, oklch(60.6% 0.25 292.717) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-violet-500) 10%, transparent);\n    }\n  }\n  .bg-white {\n    background-color: var(--color-white);\n  }\n  .bg-white\\/10 {\n    background-color: color-mix(in srgb, #fff 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 10%, transparent);\n    }\n  }\n  .bg-white\\/20 {\n    background-color: color-mix(in srgb, #fff 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 20%, transparent);\n    }\n  }\n  .bg-white\\/30 {\n    background-color: color-mix(in srgb, #fff 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 30%, transparent);\n    }\n  }\n  .bg-white\\/50 {\n    background-color: color-mix(in srgb, #fff 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 50%, transparent);\n    }\n  }\n  .bg-white\\/80 {\n    background-color: color-mix(in srgb, #fff 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 80%, transparent);\n    }\n  }\n  .bg-white\\/90 {\n    background-color: color-mix(in srgb, #fff 90%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-white) 90%, transparent);\n    }\n  }\n  .bg-yellow-100 {\n    background-color: var(--color-yellow-100);\n  }\n  .bg-yellow-400 {\n    background-color: var(--color-yellow-400);\n  }\n  .bg-yellow-500 {\n    background-color: var(--color-yellow-500);\n  }\n  .bg-yellow-500\\/20 {\n    background-color: color-mix(in srgb, oklch(79.5% 0.184 86.047) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-yellow-500) 20%, transparent);\n    }\n  }\n  .bg-linear-to-r {\n    --tw-gradient-position: to right;\n    @supports (background-image: linear-gradient(in lab, red, red)) {\n      --tw-gradient-position: to right in oklab;\n    }\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-b {\n    --tw-gradient-position: to bottom in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-bl {\n    --tw-gradient-position: to bottom left in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-br {\n    --tw-gradient-position: to bottom right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-r {\n    --tw-gradient-position: to right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-t {\n    --tw-gradient-position: to top in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-tr {\n    --tw-gradient-position: to top right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .from-\\[\\#1a1d18\\] {\n    --tw-gradient-from: #1a1d18;\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-background {\n    --tw-gradient-from: var(--background);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-blue-100 {\n    --tw-gradient-from: var(--color-blue-100);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-blue-500 {\n    --tw-gradient-from: var(--color-blue-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-blue-500\\/5 {\n    --tw-gradient-from: color-mix(in srgb, oklch(62.3% 0.214 259.815) 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-blue-500) 5%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-blue-500\\/20 {\n    --tw-gradient-from: color-mix(in srgb, oklch(62.3% 0.214 259.815) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-blue-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-destructive {\n    --tw-gradient-from: var(--destructive);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-destructive\\/10 {\n    --tw-gradient-from: var(--destructive);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--destructive) 10%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-emerald-400 {\n    --tw-gradient-from: var(--color-emerald-400);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-foreground {\n    --tw-gradient-from: var(--foreground);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-green-50 {\n    --tw-gradient-from: var(--color-green-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-green-100 {\n    --tw-gradient-from: var(--color-green-100);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-green-400 {\n    --tw-gradient-from: var(--color-green-400);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-green-500 {\n    --tw-gradient-from: var(--color-green-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-green-500\\/10 {\n    --tw-gradient-from: color-mix(in srgb, oklch(72.3% 0.219 149.579) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-green-500) 10%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-green-500\\/20 {\n    --tw-gradient-from: color-mix(in srgb, oklch(72.3% 0.219 149.579) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-green-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-indigo-500\\/20 {\n    --tw-gradient-from: color-mix(in srgb, oklch(58.5% 0.233 277.117) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-indigo-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-muted\\/5 {\n    --tw-gradient-from: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--muted) 5%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-muted\\/30 {\n    --tw-gradient-from: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--muted) 30%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-muted\\/50 {\n    --tw-gradient-from: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--muted) 50%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-orange-500\\/20 {\n    --tw-gradient-from: color-mix(in srgb, oklch(70.5% 0.213 47.604) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-orange-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-primary {\n    --tw-gradient-from: var(--primary);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-primary\\/5 {\n    --tw-gradient-from: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--primary) 5%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-primary\\/10 {\n    --tw-gradient-from: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--primary) 10%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-primary\\/20 {\n    --tw-gradient-from: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--primary) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-purple-500 {\n    --tw-gradient-from: var(--color-purple-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-purple-500\\/20 {\n    --tw-gradient-from: color-mix(in srgb, oklch(62.7% 0.265 303.9) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-purple-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-purple-500\\/30 {\n    --tw-gradient-from: color-mix(in srgb, oklch(62.7% 0.265 303.9) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-purple-500) 30%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-red-50 {\n    --tw-gradient-from: var(--color-red-50);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-red-100 {\n    --tw-gradient-from: var(--color-red-100);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-secondary {\n    --tw-gradient-from: var(--secondary);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-secondary\\/30 {\n    --tw-gradient-from: var(--secondary);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--secondary) 30%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-secondary\\/40 {\n    --tw-gradient-from: var(--secondary);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--secondary) 40%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-slate-800\\/80 {\n    --tw-gradient-from: color-mix(in srgb, oklch(27.9% 0.041 260.031) 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-slate-800) 80%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-slate-900 {\n    --tw-gradient-from: var(--color-slate-900);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-slate-950 {\n    --tw-gradient-from: var(--color-slate-950);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-transparent {\n    --tw-gradient-from: transparent;\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-violet-500 {\n    --tw-gradient-from: var(--color-violet-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-violet-500\\/30 {\n    --tw-gradient-from: color-mix(in srgb, oklch(60.6% 0.25 292.717) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-violet-500) 30%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-yellow-100 {\n    --tw-gradient-from: var(--color-yellow-100);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-yellow-400 {\n    --tw-gradient-from: var(--color-yellow-400);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-yellow-500 {\n    --tw-gradient-from: var(--color-yellow-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-20\\% {\n    --tw-gradient-from-position: 20%;\n  }\n  .via-black {\n    --tw-gradient-via: var(--color-black);\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-foreground\\/80 {\n    --tw-gradient-via: var(--foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--foreground) 80%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-primary {\n    --tw-gradient-via: var(--primary);\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-purple-200 {\n    --tw-gradient-via: var(--color-purple-200);\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-purple-500\\/20 {\n    --tw-gradient-via: color-mix(in srgb, oklch(62.7% 0.265 303.9) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-purple-500) 20%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-violet-200 {\n    --tw-gradient-via: var(--color-violet-200);\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-violet-500\\/10 {\n    --tw-gradient-via: color-mix(in srgb, oklch(60.6% 0.25 292.717) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-violet-500) 10%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-violet-500\\/20 {\n    --tw-gradient-via: color-mix(in srgb, oklch(60.6% 0.25 292.717) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-violet-500) 20%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .to-\\[\\#2a2e26\\] {\n    --tw-gradient-to: #2a2e26;\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-background {\n    --tw-gradient-to: var(--background);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-blue-200 {\n    --tw-gradient-to: var(--color-blue-200);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-blue-500\\/20 {\n    --tw-gradient-to: color-mix(in srgb, oklch(62.3% 0.214 259.815) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-blue-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-blue-700 {\n    --tw-gradient-to: var(--color-blue-700);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-cyan-500\\/5 {\n    --tw-gradient-to: color-mix(in srgb, oklch(71.5% 0.143 215.221) 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-cyan-500) 5%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-emerald-500 {\n    --tw-gradient-to: var(--color-emerald-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-emerald-500\\/5 {\n    --tw-gradient-to: color-mix(in srgb, oklch(69.6% 0.17 162.48) 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-emerald-500) 5%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-emerald-500\\/10 {\n    --tw-gradient-to: color-mix(in srgb, oklch(69.6% 0.17 162.48) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-emerald-500) 10%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-emerald-500\\/20 {\n    --tw-gradient-to: color-mix(in srgb, oklch(69.6% 0.17 162.48) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-emerald-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-gray-800\\/80 {\n    --tw-gradient-to: color-mix(in srgb, oklch(27.8% 0.033 256.848) 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-gray-800) 80%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-gray-900 {\n    --tw-gradient-to: var(--color-gray-900);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-green-50\\/50 {\n    --tw-gradient-to: color-mix(in srgb, oklch(98.2% 0.018 155.826) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-green-50) 50%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-green-200 {\n    --tw-gradient-to: var(--color-green-200);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-green-500 {\n    --tw-gradient-to: var(--color-green-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-green-600 {\n    --tw-gradient-to: var(--color-green-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-green-700 {\n    --tw-gradient-to: var(--color-green-700);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-muted {\n    --tw-gradient-to: var(--muted);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-muted-foreground {\n    --tw-gradient-to: var(--muted-foreground);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-muted-foreground\\/70 {\n    --tw-gradient-to: var(--muted-foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--muted-foreground) 70%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-muted\\/10 {\n    --tw-gradient-to: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--muted) 10%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-muted\\/20 {\n    --tw-gradient-to: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--muted) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-muted\\/30 {\n    --tw-gradient-to: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--muted) 30%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-muted\\/50 {\n    --tw-gradient-to: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--muted) 50%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-muted\\/70 {\n    --tw-gradient-to: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--muted) 70%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-orange-500 {\n    --tw-gradient-to: var(--color-orange-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-pink-500\\/20 {\n    --tw-gradient-to: color-mix(in srgb, oklch(65.6% 0.241 354.308) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-pink-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-primary {\n    --tw-gradient-to: var(--primary);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-primary\\/50 {\n    --tw-gradient-to: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--primary) 50%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-primary\\/70 {\n    --tw-gradient-to: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--primary) 70%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-purple-500\\/20 {\n    --tw-gradient-to: color-mix(in srgb, oklch(62.7% 0.265 303.9) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-purple-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-purple-700 {\n    --tw-gradient-to: var(--color-purple-700);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-red-50\\/50 {\n    --tw-gradient-to: color-mix(in srgb, oklch(97.1% 0.013 17.38) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-red-50) 50%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-red-200 {\n    --tw-gradient-to: var(--color-red-200);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-red-500\\/10 {\n    --tw-gradient-to: color-mix(in srgb, oklch(63.7% 0.237 25.331) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-red-500) 10%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-red-500\\/20 {\n    --tw-gradient-to: color-mix(in srgb, oklch(63.7% 0.237 25.331) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-red-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-red-600 {\n    --tw-gradient-to: var(--color-red-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-secondary {\n    --tw-gradient-to: var(--secondary);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-secondary\\/5 {\n    --tw-gradient-to: var(--secondary);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--secondary) 5%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-secondary\\/10 {\n    --tw-gradient-to: var(--secondary);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--secondary) 10%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-secondary\\/20 {\n    --tw-gradient-to: var(--secondary);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--secondary) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-secondary\\/30 {\n    --tw-gradient-to: var(--secondary);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--secondary) 30%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-sky-500 {\n    --tw-gradient-to: var(--color-sky-500);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-slate-800 {\n    --tw-gradient-to: var(--color-slate-800);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-transparent {\n    --tw-gradient-to: transparent;\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-yellow-200 {\n    --tw-gradient-to: var(--color-yellow-200);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-yellow-700 {\n    --tw-gradient-to: var(--color-yellow-700);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .\\[mask-image\\:linear-gradient\\(0deg\\,transparent\\,black\\)\\] {\n    mask-image: linear-gradient(0deg,transparent,black);\n  }\n  .bg-clip-text {\n    background-clip: text;\n  }\n  .fill-blue-600 {\n    fill: var(--color-blue-600);\n  }\n  .fill-current {\n    fill: currentcolor;\n  }\n  .fill-gray-600 {\n    fill: var(--color-gray-600);\n  }\n  .fill-gray-700 {\n    fill: var(--color-gray-700);\n  }\n  .fill-gray-800 {\n    fill: var(--color-gray-800);\n  }\n  .fill-primary {\n    fill: var(--primary);\n  }\n  .fill-red-600 {\n    fill: var(--color-red-600);\n  }\n  .fill-violet-500 {\n    fill: var(--color-violet-500);\n  }\n  .stroke-blue-500 {\n    stroke: var(--color-blue-500);\n  }\n  .stroke-current {\n    stroke: currentcolor;\n  }\n  .stroke-gray-400 {\n    stroke: var(--color-gray-400);\n  }\n  .stroke-gray-500 {\n    stroke: var(--color-gray-500);\n  }\n  .stroke-gray-700 {\n    stroke: var(--color-gray-700);\n  }\n  .stroke-purple-500 {\n    stroke: var(--color-purple-500);\n  }\n  .object-cover {\n    object-fit: cover;\n  }\n  .object-bottom {\n    object-position: bottom;\n  }\n  .p-0 {\n    padding: calc(var(--spacing) * 0);\n  }\n  .p-1 {\n    padding: calc(var(--spacing) * 1);\n  }\n  .p-2 {\n    padding: calc(var(--spacing) * 2);\n  }\n  .p-3 {\n    padding: calc(var(--spacing) * 3);\n  }\n  .p-4 {\n    padding: calc(var(--spacing) * 4);\n  }\n  .p-5 {\n    padding: calc(var(--spacing) * 5);\n  }\n  .p-6 {\n    padding: calc(var(--spacing) * 6);\n  }\n  .p-\\[3px\\] {\n    padding: 3px;\n  }\n  .p-px {\n    padding: 1px;\n  }\n  .px-0 {\n    padding-inline: calc(var(--spacing) * 0);\n  }\n  .px-1 {\n    padding-inline: calc(var(--spacing) * 1);\n  }\n  .px-1\\.5 {\n    padding-inline: calc(var(--spacing) * 1.5);\n  }\n  .px-2 {\n    padding-inline: calc(var(--spacing) * 2);\n  }\n  .px-2\\.5 {\n    padding-inline: calc(var(--spacing) * 2.5);\n  }\n  .px-3 {\n    padding-inline: calc(var(--spacing) * 3);\n  }\n  .px-4 {\n    padding-inline: calc(var(--spacing) * 4);\n  }\n  .px-6 {\n    padding-inline: calc(var(--spacing) * 6);\n  }\n  .px-8 {\n    padding-inline: calc(var(--spacing) * 8);\n  }\n  .py-0\\.5 {\n    padding-block: calc(var(--spacing) * 0.5);\n  }\n  .py-1 {\n    padding-block: calc(var(--spacing) * 1);\n  }\n  .py-1\\.5 {\n    padding-block: calc(var(--spacing) * 1.5);\n  }\n  .py-2 {\n    padding-block: calc(var(--spacing) * 2);\n  }\n  .py-3 {\n    padding-block: calc(var(--spacing) * 3);\n  }\n  .py-4 {\n    padding-block: calc(var(--spacing) * 4);\n  }\n  .py-6 {\n    padding-block: calc(var(--spacing) * 6);\n  }\n  .py-8 {\n    padding-block: calc(var(--spacing) * 8);\n  }\n  .py-10 {\n    padding-block: calc(var(--spacing) * 10);\n  }\n  .py-12 {\n    padding-block: calc(var(--spacing) * 12);\n  }\n  .py-14 {\n    padding-block: calc(var(--spacing) * 14);\n  }\n  .py-16 {\n    padding-block: calc(var(--spacing) * 16);\n  }\n  .py-20 {\n    padding-block: calc(var(--spacing) * 20);\n  }\n  .pt-0 {\n    padding-top: calc(var(--spacing) * 0);\n  }\n  .pt-2 {\n    padding-top: calc(var(--spacing) * 2);\n  }\n  .pt-3 {\n    padding-top: calc(var(--spacing) * 3);\n  }\n  .pt-6 {\n    padding-top: calc(var(--spacing) * 6);\n  }\n  .pt-16 {\n    padding-top: calc(var(--spacing) * 16);\n  }\n  .pt-36 {\n    padding-top: calc(var(--spacing) * 36);\n  }\n  .pr-2 {\n    padding-right: calc(var(--spacing) * 2);\n  }\n  .pr-8 {\n    padding-right: calc(var(--spacing) * 8);\n  }\n  .pr-20 {\n    padding-right: calc(var(--spacing) * 20);\n  }\n  .pb-1 {\n    padding-bottom: calc(var(--spacing) * 1);\n  }\n  .pb-3 {\n    padding-bottom: calc(var(--spacing) * 3);\n  }\n  .pb-4 {\n    padding-bottom: calc(var(--spacing) * 4);\n  }\n  .pb-6 {\n    padding-bottom: calc(var(--spacing) * 6);\n  }\n  .pb-20 {\n    padding-bottom: calc(var(--spacing) * 20);\n  }\n  .pl-2 {\n    padding-left: calc(var(--spacing) * 2);\n  }\n  .pl-8 {\n    padding-left: calc(var(--spacing) * 8);\n  }\n  .pl-10 {\n    padding-left: calc(var(--spacing) * 10);\n  }\n  .text-center {\n    text-align: center;\n  }\n  .text-left {\n    text-align: left;\n  }\n  .align-middle {\n    vertical-align: middle;\n  }\n  .font-mono {\n    font-family: var(--font-geist-mono);\n  }\n  .font-serif {\n    font-family: var(--font-serif);\n  }\n  .text-2xl {\n    font-size: var(--text-2xl);\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\n  }\n  .text-3xl {\n    font-size: var(--text-3xl);\n    line-height: var(--tw-leading, var(--text-3xl--line-height));\n  }\n  .text-4xl {\n    font-size: var(--text-4xl);\n    line-height: var(--tw-leading, var(--text-4xl--line-height));\n  }\n  .text-5xl {\n    font-size: var(--text-5xl);\n    line-height: var(--tw-leading, var(--text-5xl--line-height));\n  }\n  .text-6xl {\n    font-size: var(--text-6xl);\n    line-height: var(--tw-leading, var(--text-6xl--line-height));\n  }\n  .text-base {\n    font-size: var(--text-base);\n    line-height: var(--tw-leading, var(--text-base--line-height));\n  }\n  .text-lg {\n    font-size: var(--text-lg);\n    line-height: var(--tw-leading, var(--text-lg--line-height));\n  }\n  .text-sm {\n    font-size: var(--text-sm);\n    line-height: var(--tw-leading, var(--text-sm--line-height));\n  }\n  .text-xl {\n    font-size: var(--text-xl);\n    line-height: var(--tw-leading, var(--text-xl--line-height));\n  }\n  .text-xs {\n    font-size: var(--text-xs);\n    line-height: var(--tw-leading, var(--text-xs--line-height));\n  }\n  .text-\\[7px\\] {\n    font-size: 7px;\n  }\n  .leading-4 {\n    --tw-leading: calc(var(--spacing) * 4);\n    line-height: calc(var(--spacing) * 4);\n  }\n  .leading-5 {\n    --tw-leading: calc(var(--spacing) * 5);\n    line-height: calc(var(--spacing) * 5);\n  }\n  .leading-7 {\n    --tw-leading: calc(var(--spacing) * 7);\n    line-height: calc(var(--spacing) * 7);\n  }\n  .leading-\\[1\\.2\\] {\n    --tw-leading: 1.2;\n    line-height: 1.2;\n  }\n  .leading-none {\n    --tw-leading: 1;\n    line-height: 1;\n  }\n  .leading-relaxed {\n    --tw-leading: var(--leading-relaxed);\n    line-height: var(--leading-relaxed);\n  }\n  .leading-tight {\n    --tw-leading: var(--leading-tight);\n    line-height: var(--leading-tight);\n  }\n  .font-bold {\n    --tw-font-weight: var(--font-weight-bold);\n    font-weight: var(--font-weight-bold);\n  }\n  .font-extralight {\n    --tw-font-weight: var(--font-weight-extralight);\n    font-weight: var(--font-weight-extralight);\n  }\n  .font-light {\n    --tw-font-weight: var(--font-weight-light);\n    font-weight: var(--font-weight-light);\n  }\n  .font-medium {\n    --tw-font-weight: var(--font-weight-medium);\n    font-weight: var(--font-weight-medium);\n  }\n  .font-normal {\n    --tw-font-weight: var(--font-weight-normal);\n    font-weight: var(--font-weight-normal);\n  }\n  .font-semibold {\n    --tw-font-weight: var(--font-weight-semibold);\n    font-weight: var(--font-weight-semibold);\n  }\n  .font-thin {\n    --tw-font-weight: var(--font-weight-thin);\n    font-weight: var(--font-weight-thin);\n  }\n  .tracking-\\[0\\.2em\\] {\n    --tw-tracking: 0.2em;\n    letter-spacing: 0.2em;\n  }\n  .tracking-tight {\n    --tw-tracking: var(--tracking-tight);\n    letter-spacing: var(--tracking-tight);\n  }\n  .tracking-tighter {\n    --tw-tracking: var(--tracking-tighter);\n    letter-spacing: var(--tracking-tighter);\n  }\n  .tracking-wider {\n    --tw-tracking: var(--tracking-wider);\n    letter-spacing: var(--tracking-wider);\n  }\n  .tracking-widest {\n    --tw-tracking: var(--tracking-widest);\n    letter-spacing: var(--tracking-widest);\n  }\n  .text-balance {\n    text-wrap: balance;\n  }\n  .break-words {\n    overflow-wrap: break-word;\n  }\n  .whitespace-nowrap {\n    white-space: nowrap;\n  }\n  .text-\\[\\#e6e1d7\\] {\n    color: #e6e1d7;\n  }\n  .text-black {\n    color: var(--color-black);\n  }\n  .text-blue-500 {\n    color: var(--color-blue-500);\n  }\n  .text-blue-600 {\n    color: var(--color-blue-600);\n  }\n  .text-blue-700 {\n    color: var(--color-blue-700);\n  }\n  .text-card-foreground {\n    color: var(--card-foreground);\n  }\n  .text-current {\n    color: currentcolor;\n  }\n  .text-destructive {\n    color: var(--destructive);\n  }\n  .text-foreground {\n    color: var(--foreground);\n  }\n  .text-foreground\\/50 {\n    color: var(--foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--foreground) 50%, transparent);\n    }\n  }\n  .text-foreground\\/60 {\n    color: var(--foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--foreground) 60%, transparent);\n    }\n  }\n  .text-gray-300 {\n    color: var(--color-gray-300);\n  }\n  .text-gray-500 {\n    color: var(--color-gray-500);\n  }\n  .text-gray-800 {\n    color: var(--color-gray-800);\n  }\n  .text-gray-900 {\n    color: var(--color-gray-900);\n  }\n  .text-green-300 {\n    color: var(--color-green-300);\n  }\n  .text-green-400 {\n    color: var(--color-green-400);\n  }\n  .text-green-500 {\n    color: var(--color-green-500);\n  }\n  .text-green-600 {\n    color: var(--color-green-600);\n  }\n  .text-green-800 {\n    color: var(--color-green-800);\n  }\n  .text-indigo-400 {\n    color: var(--color-indigo-400);\n  }\n  .text-indigo-600 {\n    color: var(--color-indigo-600);\n  }\n  .text-muted-foreground {\n    color: var(--muted-foreground);\n  }\n  .text-muted\\/20 {\n    color: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--muted) 20%, transparent);\n    }\n  }\n  .text-neutral-600 {\n    color: var(--color-neutral-600);\n  }\n  .text-orange-500 {\n    color: var(--color-orange-500);\n  }\n  .text-orange-600 {\n    color: var(--color-orange-600);\n  }\n  .text-popover-foreground {\n    color: var(--popover-foreground);\n  }\n  .text-primary {\n    color: var(--primary);\n  }\n  .text-primary-foreground {\n    color: var(--primary-foreground);\n  }\n  .text-purple-600 {\n    color: var(--color-purple-600);\n  }\n  .text-red-500 {\n    color: var(--color-red-500);\n  }\n  .text-red-600 {\n    color: var(--color-red-600);\n  }\n  .text-red-800 {\n    color: var(--color-red-800);\n  }\n  .text-secondary-foreground {\n    color: var(--secondary-foreground);\n  }\n  .text-secondary-foreground\\/70 {\n    color: var(--secondary-foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--secondary-foreground) 70%, transparent);\n    }\n  }\n  .text-sidebar-foreground {\n    color: var(--sidebar-foreground);\n  }\n  .text-sidebar-foreground\\/70 {\n    color: var(--sidebar-foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--sidebar-foreground) 70%, transparent);\n    }\n  }\n  .text-slate-300 {\n    color: var(--color-slate-300);\n  }\n  .text-slate-400 {\n    color: var(--color-slate-400);\n  }\n  .text-transparent {\n    color: transparent;\n  }\n  .text-violet-500 {\n    color: var(--color-violet-500);\n  }\n  .text-white {\n    color: var(--color-white);\n  }\n  .text-white\\/70 {\n    color: color-mix(in srgb, #fff 70%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-white) 70%, transparent);\n    }\n  }\n  .text-yellow-500 {\n    color: var(--color-yellow-500);\n  }\n  .text-yellow-600 {\n    color: var(--color-yellow-600);\n  }\n  .text-yellow-800 {\n    color: var(--color-yellow-800);\n  }\n  .text-zinc-600 {\n    color: var(--color-zinc-600);\n  }\n  .capitalize {\n    text-transform: capitalize;\n  }\n  .uppercase {\n    text-transform: uppercase;\n  }\n  .italic {\n    font-style: italic;\n  }\n  .not-italic {\n    font-style: normal;\n  }\n  .tabular-nums {\n    --tw-numeric-spacing: tabular-nums;\n    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);\n  }\n  .underline-offset-4 {\n    text-underline-offset: 4px;\n  }\n  .opacity-0 {\n    opacity: 0%;\n  }\n  .opacity-5 {\n    opacity: 5%;\n  }\n  .opacity-10 {\n    opacity: 10%;\n  }\n  .opacity-20 {\n    opacity: 20%;\n  }\n  .opacity-30 {\n    opacity: 30%;\n  }\n  .opacity-40 {\n    opacity: 40%;\n  }\n  .opacity-50 {\n    opacity: 50%;\n  }\n  .opacity-60 {\n    opacity: 60%;\n  }\n  .opacity-70 {\n    opacity: 70%;\n  }\n  .opacity-75 {\n    opacity: 75%;\n  }\n  .opacity-80 {\n    opacity: 80%;\n  }\n  .shadow {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-2xl {\n    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-border\\)\\)\\] {\n    --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-\\[0_0_24px_rgba\\(34\\,_42\\,_53\\,_0\\.06\\)\\,_0_1px_1px_rgba\\(0\\,_0\\,_0\\,_0\\.05\\)\\,_0_0_0_1px_rgba\\(34\\,_42\\,_53\\,_0\\.04\\)\\,_0_0_4px_rgba\\(34\\,_42\\,_53\\,_0\\.08\\)\\,_0_16px_68px_rgba\\(47\\,_48\\,_55\\,_0\\.05\\)\\,_0_1px_0_rgba\\(255\\,_255\\,_255\\,_0\\.1\\)_inset\\] {\n    --tw-shadow: 0 0 24px var(--tw-shadow-color, rgba(34, 42, 53, 0.06)), 0 1px 1px var(--tw-shadow-color, rgba(0, 0, 0, 0.05)), 0 0 0 1px var(--tw-shadow-color, rgba(34, 42, 53, 0.04)), 0 0 4px var(--tw-shadow-color, rgba(34, 42, 53, 0.08)), 0 16px 68px var(--tw-shadow-color, rgba(47, 48, 55, 0.05)), 0 1px 0 var(--tw-shadow-color, rgba(255, 255, 255, 0.1)) inset;\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-\\[0px_5px_5px_0px_rgba\\(255\\,255\\,255\\,0\\.6\\)_inset\\] {\n    --tw-shadow: 0px 5px 5px 0px var(--tw-shadow-color, rgba(255,255,255,0.6)) inset;\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-lg {\n    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-md {\n    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-none {\n    --tw-shadow: 0 0 #0000;\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-sm {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-xl {\n    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-xs {\n    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .ring-0 {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .ring-1 {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .\\[box-shadow\\:0_-20px_80px_-20px_\\#d478ff2f_inset\\] {\n    box-shadow: 0 -20px 80px -20px #d478ff2f inset;\n  }\n  .\\[box-shadow\\:0_-20px_80px_-20px_\\#d478ff3f_inset\\] {\n    box-shadow: 0 -20px 80px -20px #d478ff3f inset;\n  }\n  .shadow-destructive\\/30 {\n    --tw-shadow-color: var(--destructive);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--destructive) 30%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-green-500\\/30 {\n    --tw-shadow-color: color-mix(in srgb, oklch(72.3% 0.219 149.579) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-green-500) 30%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-primary\\/5 {\n    --tw-shadow-color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--primary) 5%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-primary\\/30 {\n    --tw-shadow-color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--primary) 30%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-primary\\/50 {\n    --tw-shadow-color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--primary) 50%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .shadow-yellow-500\\/30 {\n    --tw-shadow-color: color-mix(in srgb, oklch(79.5% 0.184 86.047) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-yellow-500) 30%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .ring-sidebar-ring {\n    --tw-ring-color: var(--sidebar-ring);\n  }\n  .ring-violet-500\\/20 {\n    --tw-ring-color: color-mix(in srgb, oklch(60.6% 0.25 292.717) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-ring-color: color-mix(in oklab, var(--color-violet-500) 20%, transparent);\n    }\n  }\n  .ring-offset-2 {\n    --tw-ring-offset-width: 2px;\n    --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n  }\n  .ring-offset-background {\n    --tw-ring-offset-color: var(--background);\n  }\n  .outline-hidden {\n    --tw-outline-style: none;\n    outline-style: none;\n    @media (forced-colors: active) {\n      outline: 2px solid transparent;\n      outline-offset: 2px;\n    }\n  }\n  .outline {\n    outline-style: var(--tw-outline-style);\n    outline-width: 1px;\n  }\n  .blur {\n    --tw-blur: blur(8px);\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .blur-2xl {\n    --tw-blur: blur(var(--blur-2xl));\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .blur-3xl {\n    --tw-blur: blur(var(--blur-3xl));\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .blur-\\[80px\\] {\n    --tw-blur: blur(80px);\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .blur-\\[100px\\] {\n    --tw-blur: blur(100px);\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .blur-\\[118px\\] {\n    --tw-blur: blur(118px);\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .blur-xl {\n    --tw-blur: blur(var(--blur-xl));\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .grayscale {\n    --tw-grayscale: grayscale(100%);\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .backdrop-blur {\n    --tw-backdrop-blur: blur(8px);\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-blur-lg {\n    --tw-backdrop-blur: blur(var(--blur-lg));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-blur-sm {\n    --tw-backdrop-blur: blur(var(--blur-sm));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-blur-xl {\n    --tw-backdrop-blur: blur(var(--blur-xl));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .transition {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[color\\,box-shadow\\] {\n    transition-property: color,box-shadow;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[left\\,right\\,width\\] {\n    transition-property: left,right,width;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[margin\\,opacity\\] {\n    transition-property: margin,opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[width\\,height\\,padding\\] {\n    transition-property: width,height,padding;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[width\\,height\\] {\n    transition-property: width,height;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[width\\] {\n    transition-property: width;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-all {\n    transition-property: all;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-colors {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-opacity {\n    transition-property: opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-shadow {\n    transition-property: box-shadow;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-transform {\n    transition-property: transform, translate, scale, rotate;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-none {\n    transition-property: none;\n  }\n  .delay-300 {\n    transition-delay: 300ms;\n  }\n  .delay-500 {\n    transition-delay: 500ms;\n  }\n  .delay-700 {\n    transition-delay: 700ms;\n  }\n  .delay-1000 {\n    transition-delay: 1000ms;\n  }\n  .duration-200 {\n    --tw-duration: 200ms;\n    transition-duration: 200ms;\n  }\n  .duration-300 {\n    --tw-duration: 300ms;\n    transition-duration: 300ms;\n  }\n  .duration-500 {\n    --tw-duration: 500ms;\n    transition-duration: 500ms;\n  }\n  .duration-700 {\n    --tw-duration: 700ms;\n    transition-duration: 700ms;\n  }\n  .duration-1000 {\n    --tw-duration: 1000ms;\n    transition-duration: 1000ms;\n  }\n  .duration-1500 {\n    --tw-duration: 1500ms;\n    transition-duration: 1500ms;\n  }\n  .ease-in-out {\n    --tw-ease: var(--ease-in-out);\n    transition-timing-function: var(--ease-in-out);\n  }\n  .ease-linear {\n    --tw-ease: linear;\n    transition-timing-function: linear;\n  }\n  .ease-out {\n    --tw-ease: var(--ease-out);\n    transition-timing-function: var(--ease-out);\n  }\n  .outline-none {\n    --tw-outline-style: none;\n    outline-style: none;\n  }\n  .select-none {\n    -webkit-user-select: none;\n    user-select: none;\n  }\n  .\\[--duration\\:30s\\] {\n    --duration: 30s;\n  }\n  .\\[--duration\\:40s\\] {\n    --duration: 40s;\n  }\n  .\\[--duration\\:60s\\] {\n    --duration: 60s;\n  }\n  .\\[--duration\\:70s\\] {\n    --duration: 70s;\n  }\n  .\\[--gap\\:1rem\\] {\n    --gap: 1rem;\n  }\n  .\\[animation-direction\\:reverse\\] {\n    animation-direction: reverse;\n  }\n  .group-focus-within\\/menu-item\\:opacity-100 {\n    &:is(:where(.group\\/menu-item):focus-within *) {\n      opacity: 100%;\n    }\n  }\n  .group-hover\\:scale-100 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        --tw-scale-x: 100%;\n        --tw-scale-y: 100%;\n        --tw-scale-z: 100%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .group-hover\\:scale-105 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        --tw-scale-x: 105%;\n        --tw-scale-y: 105%;\n        --tw-scale-z: 105%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .group-hover\\:text-primary {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        color: var(--primary);\n      }\n    }\n  }\n  .group-hover\\:opacity-100 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .group-hover\\:\\[animation-play-state\\:paused\\] {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        animation-play-state: paused;\n      }\n    }\n  }\n  .group-hover\\/menu-item\\:opacity-100 {\n    &:is(:where(.group\\/menu-item):hover *) {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .group-has-data-\\[collapsible\\=icon\\]\\/sidebar-wrapper\\:h-\\(--header-height\\) {\n    &:is(:where(.group\\/sidebar-wrapper):has(*[data-collapsible=\"icon\"]) *) {\n      height: var(--header-height);\n    }\n  }\n  .group-has-data-\\[sidebar\\=menu-action\\]\\/menu-item\\:pr-8 {\n    &:is(:where(.group\\/menu-item):has(*[data-sidebar=\"menu-action\"]) *) {\n      padding-right: calc(var(--spacing) * 8);\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:-mt-8 {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      margin-top: calc(var(--spacing) * -8);\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:hidden {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      display: none;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:size-8\\! {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      width: calc(var(--spacing) * 8) !important;\n      height: calc(var(--spacing) * 8) !important;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:w-\\(--sidebar-width-icon\\) {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      width: var(--sidebar-width-icon);\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)\\+\\(--spacing\\(4\\)\\)\\)\\] {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      width: calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)));\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)\\+\\(--spacing\\(4\\)\\)\\+2px\\)\\] {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      width: calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)) + 2px);\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:overflow-hidden {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      overflow: hidden;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:p-0\\! {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      padding: calc(var(--spacing) * 0) !important;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:p-2\\! {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      padding: calc(var(--spacing) * 2) !important;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:opacity-0 {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      opacity: 0%;\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:right-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\] {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      right: calc(var(--sidebar-width) * -1);\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:left-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\] {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      left: calc(var(--sidebar-width) * -1);\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:w-0 {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      width: calc(var(--spacing) * 0);\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:translate-x-0 {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      --tw-translate-x: calc(var(--spacing) * 0);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .group-data-\\[disabled\\=true\\]\\:pointer-events-none {\n    &:is(:where(.group)[data-disabled=\"true\"] *) {\n      pointer-events: none;\n    }\n  }\n  .group-data-\\[disabled\\=true\\]\\:opacity-50 {\n    &:is(:where(.group)[data-disabled=\"true\"] *) {\n      opacity: 50%;\n    }\n  }\n  .group-data-\\[side\\=left\\]\\:-right-4 {\n    &:is(:where(.group)[data-side=\"left\"] *) {\n      right: calc(var(--spacing) * -4);\n    }\n  }\n  .group-data-\\[side\\=left\\]\\:border-r {\n    &:is(:where(.group)[data-side=\"left\"] *) {\n      border-right-style: var(--tw-border-style);\n      border-right-width: 1px;\n    }\n  }\n  .group-data-\\[side\\=right\\]\\:left-0 {\n    &:is(:where(.group)[data-side=\"right\"] *) {\n      left: calc(var(--spacing) * 0);\n    }\n  }\n  .group-data-\\[side\\=right\\]\\:rotate-180 {\n    &:is(:where(.group)[data-side=\"right\"] *) {\n      rotate: 180deg;\n    }\n  }\n  .group-data-\\[side\\=right\\]\\:border-l {\n    &:is(:where(.group)[data-side=\"right\"] *) {\n      border-left-style: var(--tw-border-style);\n      border-left-width: 1px;\n    }\n  }\n  .group-data-\\[variant\\=floating\\]\\:rounded-lg {\n    &:is(:where(.group)[data-variant=\"floating\"] *) {\n      border-radius: var(--radius);\n    }\n  }\n  .group-data-\\[variant\\=floating\\]\\:border {\n    &:is(:where(.group)[data-variant=\"floating\"] *) {\n      border-style: var(--tw-border-style);\n      border-width: 1px;\n    }\n  }\n  .group-data-\\[variant\\=floating\\]\\:border-sidebar-border {\n    &:is(:where(.group)[data-variant=\"floating\"] *) {\n      border-color: var(--sidebar-border);\n    }\n  }\n  .group-data-\\[variant\\=floating\\]\\:shadow-sm {\n    &:is(:where(.group)[data-variant=\"floating\"] *) {\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .group-data-\\[vaul-drawer-direction\\=bottom\\]\\/drawer-content\\:block {\n    &:is(:where(.group\\/drawer-content)[data-vaul-drawer-direction=\"bottom\"] *) {\n      display: block;\n    }\n  }\n  .group-data-\\[vaul-drawer-direction\\=bottom\\]\\/drawer-content\\:text-center {\n    &:is(:where(.group\\/drawer-content)[data-vaul-drawer-direction=\"bottom\"] *) {\n      text-align: center;\n    }\n  }\n  .group-data-\\[vaul-drawer-direction\\=top\\]\\/drawer-content\\:text-center {\n    &:is(:where(.group\\/drawer-content)[data-vaul-drawer-direction=\"top\"] *) {\n      text-align: center;\n    }\n  }\n  .peer-hover\\/menu-button\\:text-sidebar-accent-foreground {\n    &:is(:where(.peer\\/menu-button):hover ~ *) {\n      @media (hover: hover) {\n        color: var(--sidebar-accent-foreground);\n      }\n    }\n  }\n  .peer-disabled\\:cursor-not-allowed {\n    &:is(:where(.peer):disabled ~ *) {\n      cursor: not-allowed;\n    }\n  }\n  .peer-disabled\\:opacity-50 {\n    &:is(:where(.peer):disabled ~ *) {\n      opacity: 50%;\n    }\n  }\n  .peer-data-\\[active\\=true\\]\\/menu-button\\:text-sidebar-accent-foreground {\n    &:is(:where(.peer\\/menu-button)[data-active=\"true\"] ~ *) {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .peer-data-\\[size\\=default\\]\\/menu-button\\:top-1\\.5 {\n    &:is(:where(.peer\\/menu-button)[data-size=\"default\"] ~ *) {\n      top: calc(var(--spacing) * 1.5);\n    }\n  }\n  .peer-data-\\[size\\=lg\\]\\/menu-button\\:top-2\\.5 {\n    &:is(:where(.peer\\/menu-button)[data-size=\"lg\"] ~ *) {\n      top: calc(var(--spacing) * 2.5);\n    }\n  }\n  .peer-data-\\[size\\=sm\\]\\/menu-button\\:top-1 {\n    &:is(:where(.peer\\/menu-button)[data-size=\"sm\"] ~ *) {\n      top: calc(var(--spacing) * 1);\n    }\n  }\n  .selection\\:bg-primary {\n    & *::selection {\n      background-color: var(--primary);\n    }\n    &::selection {\n      background-color: var(--primary);\n    }\n  }\n  .selection\\:text-primary-foreground {\n    & *::selection {\n      color: var(--primary-foreground);\n    }\n    &::selection {\n      color: var(--primary-foreground);\n    }\n  }\n  .file\\:inline-flex {\n    &::file-selector-button {\n      display: inline-flex;\n    }\n  }\n  .file\\:h-7 {\n    &::file-selector-button {\n      height: calc(var(--spacing) * 7);\n    }\n  }\n  .file\\:border-0 {\n    &::file-selector-button {\n      border-style: var(--tw-border-style);\n      border-width: 0px;\n    }\n  }\n  .file\\:bg-transparent {\n    &::file-selector-button {\n      background-color: transparent;\n    }\n  }\n  .file\\:text-sm {\n    &::file-selector-button {\n      font-size: var(--text-sm);\n      line-height: var(--tw-leading, var(--text-sm--line-height));\n    }\n  }\n  .file\\:font-medium {\n    &::file-selector-button {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .file\\:text-foreground {\n    &::file-selector-button {\n      color: var(--foreground);\n    }\n  }\n  .placeholder\\:text-muted-foreground {\n    &::placeholder {\n      color: var(--muted-foreground);\n    }\n  }\n  .after\\:absolute {\n    &::after {\n      content: var(--tw-content);\n      position: absolute;\n    }\n  }\n  .after\\:-inset-2 {\n    &::after {\n      content: var(--tw-content);\n      inset: calc(var(--spacing) * -2);\n    }\n  }\n  .after\\:inset-y-0 {\n    &::after {\n      content: var(--tw-content);\n      inset-block: calc(var(--spacing) * 0);\n    }\n  }\n  .after\\:left-1\\/2 {\n    &::after {\n      content: var(--tw-content);\n      left: calc(1/2 * 100%);\n    }\n  }\n  .after\\:w-\\[2px\\] {\n    &::after {\n      content: var(--tw-content);\n      width: 2px;\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:after\\:left-full {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      &::after {\n        content: var(--tw-content);\n        left: 100%;\n      }\n    }\n  }\n  .first\\:rounded-l-md {\n    &:first-child {\n      border-top-left-radius: calc(var(--radius) - 2px);\n      border-bottom-left-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .last\\:rounded-r-md {\n    &:last-child {\n      border-top-right-radius: calc(var(--radius) - 2px);\n      border-bottom-right-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .last\\:border-b-0 {\n    &:last-child {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 0px;\n    }\n  }\n  .hover\\:-translate-y-0\\.5 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-translate-y: calc(var(--spacing) * -0.5);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .hover\\:-translate-y-1 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-translate-y: calc(var(--spacing) * -1);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .hover\\:scale-110 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-scale-x: 110%;\n        --tw-scale-y: 110%;\n        --tw-scale-z: 110%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .hover\\:border-primary\\/10 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          border-color: color-mix(in oklab, var(--primary) 10%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:border-primary\\/20 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          border-color: color-mix(in oklab, var(--primary) 20%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:border-primary\\/30 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          border-color: color-mix(in oklab, var(--primary) 30%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:border-primary\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          border-color: color-mix(in oklab, var(--primary) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-accent {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--accent);\n      }\n    }\n  }\n  .hover\\:bg-accent\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--accent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--accent) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-black\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, #000 80%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-black) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-destructive\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--destructive) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-destructive\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--destructive) 90%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-gray-100 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-gray-100);\n      }\n    }\n  }\n  .hover\\:bg-green-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-green-700);\n      }\n    }\n  }\n  .hover\\:bg-muted {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--muted);\n      }\n    }\n  }\n  .hover\\:bg-muted\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--muted);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--muted) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-primary {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--primary);\n      }\n    }\n  }\n  .hover\\:bg-primary\\/5 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--primary) 5%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-primary\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--primary) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-primary\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--primary) 90%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-red-50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-red-50);\n      }\n    }\n  }\n  .hover\\:bg-secondary\\/20 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--secondary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--secondary) 20%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-secondary\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--secondary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--secondary) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-secondary\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--secondary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--secondary) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-sidebar-accent {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--sidebar-accent);\n      }\n    }\n  }\n  .hover\\:bg-white\\/15 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, #fff 15%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-white) 15%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-white\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, #fff 90%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-white) 90%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:from-primary\\/10 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-from: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-gradient-from: color-mix(in oklab, var(--primary) 10%, transparent);\n        }\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:from-primary\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-from: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-gradient-from: color-mix(in oklab, var(--primary) 90%, transparent);\n        }\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:to-secondary\\/10 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-to: var(--secondary);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-gradient-to: color-mix(in oklab, var(--secondary) 10%, transparent);\n        }\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:to-secondary\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-to: var(--secondary);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-gradient-to: color-mix(in oklab, var(--secondary) 90%, transparent);\n        }\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:text-accent-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--accent-foreground);\n      }\n    }\n  }\n  .hover\\:text-blue-600 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-blue-600);\n      }\n    }\n  }\n  .hover\\:text-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--foreground);\n      }\n    }\n  }\n  .hover\\:text-muted-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--muted-foreground);\n      }\n    }\n  }\n  .hover\\:text-primary-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--primary-foreground);\n      }\n    }\n  }\n  .hover\\:text-primary\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          color: color-mix(in oklab, var(--primary) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:text-red-600 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-red-600);\n      }\n    }\n  }\n  .hover\\:text-sidebar-accent-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--sidebar-accent-foreground);\n      }\n    }\n  }\n  .hover\\:text-zinc-800 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-zinc-800);\n      }\n    }\n  }\n  .hover\\:no-underline {\n    &:hover {\n      @media (hover: hover) {\n        text-decoration-line: none;\n      }\n    }\n  }\n  .hover\\:underline {\n    &:hover {\n      @media (hover: hover) {\n        text-decoration-line: underline;\n      }\n    }\n  }\n  .hover\\:opacity-100 {\n    &:hover {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .hover\\:shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-accent\\)\\)\\] {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-lg {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-md {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-primary\\/5 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow-color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--primary) 5%, transparent) var(--tw-shadow-alpha), transparent);\n        }\n      }\n    }\n  }\n  .hover\\:group-data-\\[collapsible\\=offcanvas\\]\\:bg-sidebar {\n    &:hover {\n      @media (hover: hover) {\n        &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n          background-color: var(--sidebar);\n        }\n      }\n    }\n  }\n  .hover\\:after\\:bg-sidebar-border {\n    &:hover {\n      @media (hover: hover) {\n        &::after {\n          content: var(--tw-content);\n          background-color: var(--sidebar-border);\n        }\n      }\n    }\n  }\n  .focus\\:z-10 {\n    &:focus {\n      z-index: 10;\n    }\n  }\n  .focus\\:border-primary {\n    &:focus {\n      border-color: var(--primary);\n    }\n  }\n  .focus\\:bg-accent {\n    &:focus {\n      background-color: var(--accent);\n    }\n  }\n  .focus\\:bg-red-50 {\n    &:focus {\n      background-color: var(--color-red-50);\n    }\n  }\n  .focus\\:text-accent-foreground {\n    &:focus {\n      color: var(--accent-foreground);\n    }\n  }\n  .focus\\:text-destructive {\n    &:focus {\n      color: var(--destructive);\n    }\n  }\n  .focus\\:ring-2 {\n    &:focus {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus\\:ring-primary\\/20 {\n    &:focus {\n      --tw-ring-color: var(--primary);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--primary) 20%, transparent);\n      }\n    }\n  }\n  .focus\\:ring-ring {\n    &:focus {\n      --tw-ring-color: var(--ring);\n    }\n  }\n  .focus\\:ring-offset-2 {\n    &:focus {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus\\:outline-hidden {\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .focus\\:outline-none {\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .focus-visible\\:z-10 {\n    &:focus-visible {\n      z-index: 10;\n    }\n  }\n  .focus-visible\\:border-ring {\n    &:focus-visible {\n      border-color: var(--ring);\n    }\n  }\n  .focus-visible\\:ring-2 {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-\\[3px\\] {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-ring {\n    &:focus-visible {\n      --tw-ring-color: var(--ring);\n    }\n  }\n  .focus-visible\\:ring-ring\\/50 {\n    &:focus-visible {\n      --tw-ring-color: var(--ring);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);\n      }\n    }\n  }\n  .focus-visible\\:ring-offset-2 {\n    &:focus-visible {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus-visible\\:outline-1 {\n    &:focus-visible {\n      outline-style: var(--tw-outline-style);\n      outline-width: 1px;\n    }\n  }\n  .focus-visible\\:outline-ring {\n    &:focus-visible {\n      outline-color: var(--ring);\n    }\n  }\n  .focus-visible\\:outline-none {\n    &:focus-visible {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .active\\:scale-95 {\n    &:active {\n      --tw-scale-x: 95%;\n      --tw-scale-y: 95%;\n      --tw-scale-z: 95%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .active\\:bg-sidebar-accent {\n    &:active {\n      background-color: var(--sidebar-accent);\n    }\n  }\n  .active\\:text-sidebar-accent-foreground {\n    &:active {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .disabled\\:pointer-events-none {\n    &:disabled {\n      pointer-events: none;\n    }\n  }\n  .disabled\\:cursor-not-allowed {\n    &:disabled {\n      cursor: not-allowed;\n    }\n  }\n  .disabled\\:opacity-50 {\n    &:disabled {\n      opacity: 50%;\n    }\n  }\n  .in-data-\\[side\\=left\\]\\:cursor-w-resize {\n    :where(*[data-side=\"left\"]) & {\n      cursor: w-resize;\n    }\n  }\n  .in-data-\\[side\\=right\\]\\:cursor-e-resize {\n    :where(*[data-side=\"right\"]) & {\n      cursor: e-resize;\n    }\n  }\n  .has-data-\\[slot\\=card-action\\]\\:grid-cols-\\[1fr_auto\\] {\n    &:has(*[data-slot=\"card-action\"]) {\n      grid-template-columns: 1fr auto;\n    }\n  }\n  .has-data-\\[variant\\=inset\\]\\:bg-sidebar {\n    &:has(*[data-variant=\"inset\"]) {\n      background-color: var(--sidebar);\n    }\n  }\n  .aria-disabled\\:pointer-events-none {\n    &[aria-disabled=\"true\"] {\n      pointer-events: none;\n    }\n  }\n  .aria-disabled\\:opacity-50 {\n    &[aria-disabled=\"true\"] {\n      opacity: 50%;\n    }\n  }\n  .aria-invalid\\:border-destructive {\n    &[aria-invalid=\"true\"] {\n      border-color: var(--destructive);\n    }\n  }\n  .aria-invalid\\:ring-destructive\\/20 {\n    &[aria-invalid=\"true\"] {\n      --tw-ring-color: var(--destructive);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-sidebar-accent {\n    &[data-active=\"true\"] {\n      background-color: var(--sidebar-accent);\n    }\n  }\n  .data-\\[active\\=true\\]\\:font-medium {\n    &[data-active=\"true\"] {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .data-\\[active\\=true\\]\\:text-sidebar-accent-foreground {\n    &[data-active=\"true\"] {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .data-\\[disabled\\]\\:pointer-events-none {\n    &[data-disabled] {\n      pointer-events: none;\n    }\n  }\n  .data-\\[disabled\\]\\:opacity-50 {\n    &[data-disabled] {\n      opacity: 50%;\n    }\n  }\n  .data-\\[inset\\]\\:pl-8 {\n    &[data-inset] {\n      padding-left: calc(var(--spacing) * 8);\n    }\n  }\n  .data-\\[orientation\\=horizontal\\]\\:h-px {\n    &[data-orientation=\"horizontal\"] {\n      height: 1px;\n    }\n  }\n  .data-\\[orientation\\=horizontal\\]\\:w-full {\n    &[data-orientation=\"horizontal\"] {\n      width: 100%;\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:h-4 {\n    &[data-orientation=\"vertical\"] {\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:h-full {\n    &[data-orientation=\"vertical\"] {\n      height: 100%;\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:w-px {\n    &[data-orientation=\"vertical\"] {\n      width: 1px;\n    }\n  }\n  .data-\\[placeholder\\]\\:text-muted-foreground {\n    &[data-placeholder] {\n      color: var(--muted-foreground);\n    }\n  }\n  .data-\\[side\\=bottom\\]\\:translate-y-1 {\n    &[data-side=\"bottom\"] {\n      --tw-translate-y: calc(var(--spacing) * 1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=left\\]\\:-translate-x-1 {\n    &[data-side=\"left\"] {\n      --tw-translate-x: calc(var(--spacing) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=right\\]\\:translate-x-1 {\n    &[data-side=\"right\"] {\n      --tw-translate-x: calc(var(--spacing) * 1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=top\\]\\:-translate-y-1 {\n    &[data-side=\"top\"] {\n      --tw-translate-y: calc(var(--spacing) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[size\\=default\\]\\:h-9 {\n    &[data-size=\"default\"] {\n      height: calc(var(--spacing) * 9);\n    }\n  }\n  .data-\\[size\\=sm\\]\\:h-8 {\n    &[data-size=\"sm\"] {\n      height: calc(var(--spacing) * 8);\n    }\n  }\n  .\\*\\:data-\\[slot\\=card\\]\\:bg-gradient-to-t {\n    :is(& > *) {\n      &[data-slot=\"card\"] {\n        --tw-gradient-position: to top in oklab;\n        background-image: linear-gradient(var(--tw-gradient-stops));\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=card\\]\\:from-primary\\/5 {\n    :is(& > *) {\n      &[data-slot=\"card\"] {\n        --tw-gradient-from: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-gradient-from: color-mix(in oklab, var(--primary) 5%, transparent);\n        }\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=card\\]\\:to-card {\n    :is(& > *) {\n      &[data-slot=\"card\"] {\n        --tw-gradient-to: var(--card);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=card\\]\\:shadow-xs {\n    :is(& > *) {\n      &[data-slot=\"card\"] {\n        --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:line-clamp-1 {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        overflow: hidden;\n        display: -webkit-box;\n        -webkit-box-orient: vertical;\n        -webkit-line-clamp: 1;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:flex {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        display: flex;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:items-center {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        align-items: center;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:gap-2 {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        gap: calc(var(--spacing) * 2);\n      }\n    }\n  }\n  .data-\\[slot\\=sidebar-menu-button\\]\\:\\!p-1\\.5 {\n    &[data-slot=\"sidebar-menu-button\"] {\n      padding: calc(var(--spacing) * 1.5) !important;\n    }\n  }\n  .data-\\[state\\=active\\]\\:border-primary\\/20 {\n    &[data-state=\"active\"] {\n      border-color: var(--primary);\n      @supports (color: color-mix(in lab, red, red)) {\n        border-color: color-mix(in oklab, var(--primary) 20%, transparent);\n      }\n    }\n  }\n  .data-\\[state\\=active\\]\\:bg-background {\n    &[data-state=\"active\"] {\n      background-color: var(--background);\n    }\n  }\n  .data-\\[state\\=active\\]\\:bg-gradient-to-r {\n    &[data-state=\"active\"] {\n      --tw-gradient-position: to right in oklab;\n      background-image: linear-gradient(var(--tw-gradient-stops));\n    }\n  }\n  .data-\\[state\\=active\\]\\:from-primary\\/10 {\n    &[data-state=\"active\"] {\n      --tw-gradient-from: var(--primary);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-from: color-mix(in oklab, var(--primary) 10%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .data-\\[state\\=active\\]\\:to-secondary\\/10 {\n    &[data-state=\"active\"] {\n      --tw-gradient-to: var(--secondary);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-to: color-mix(in oklab, var(--secondary) 10%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .data-\\[state\\=active\\]\\:text-primary {\n    &[data-state=\"active\"] {\n      color: var(--primary);\n    }\n  }\n  .data-\\[state\\=active\\]\\:shadow-sm {\n    &[data-state=\"active\"] {\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:translate-x-\\[calc\\(100\\%-2px\\)\\] {\n    &[data-state=\"checked\"] {\n      --tw-translate-x: calc(100% - 2px);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:border-primary {\n    &[data-state=\"checked\"] {\n      border-color: var(--primary);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:bg-primary {\n    &[data-state=\"checked\"] {\n      background-color: var(--primary);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:text-primary-foreground {\n    &[data-state=\"checked\"] {\n      color: var(--primary-foreground);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:duration-300 {\n    &[data-state=\"closed\"] {\n      --tw-duration: 300ms;\n      transition-duration: 300ms;\n    }\n  }\n  .data-\\[state\\=on\\]\\:bg-accent {\n    &[data-state=\"on\"] {\n      background-color: var(--accent);\n    }\n  }\n  .data-\\[state\\=on\\]\\:text-accent-foreground {\n    &[data-state=\"on\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-accent {\n    &[data-state=\"open\"] {\n      background-color: var(--accent);\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-secondary {\n    &[data-state=\"open\"] {\n      background-color: var(--secondary);\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-sidebar-accent {\n    &[data-state=\"open\"] {\n      background-color: var(--sidebar-accent);\n    }\n  }\n  .data-\\[state\\=open\\]\\:text-accent-foreground {\n    &[data-state=\"open\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .data-\\[state\\=open\\]\\:text-muted-foreground {\n    &[data-state=\"open\"] {\n      color: var(--muted-foreground);\n    }\n  }\n  .data-\\[state\\=open\\]\\:text-sidebar-accent-foreground {\n    &[data-state=\"open\"] {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .data-\\[state\\=open\\]\\:opacity-100 {\n    &[data-state=\"open\"] {\n      opacity: 100%;\n    }\n  }\n  .data-\\[state\\=open\\]\\:duration-500 {\n    &[data-state=\"open\"] {\n      --tw-duration: 500ms;\n      transition-duration: 500ms;\n    }\n  }\n  .data-\\[state\\=open\\]\\:hover\\:bg-sidebar-accent {\n    &[data-state=\"open\"] {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--sidebar-accent);\n        }\n      }\n    }\n  }\n  .data-\\[state\\=open\\]\\:hover\\:text-sidebar-accent-foreground {\n    &[data-state=\"open\"] {\n      &:hover {\n        @media (hover: hover) {\n          color: var(--sidebar-accent-foreground);\n        }\n      }\n    }\n  }\n  .data-\\[state\\=selected\\]\\:bg-muted {\n    &[data-state=\"selected\"] {\n      background-color: var(--muted);\n    }\n  }\n  .data-\\[state\\=unchecked\\]\\:translate-x-0 {\n    &[data-state=\"unchecked\"] {\n      --tw-translate-x: calc(var(--spacing) * 0);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[state\\=unchecked\\]\\:bg-input {\n    &[data-state=\"unchecked\"] {\n      background-color: var(--input);\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:text-destructive {\n    &[data-variant=\"destructive\"] {\n      color: var(--destructive);\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/10 {\n    &[data-variant=\"destructive\"] {\n      &:focus {\n        background-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--destructive) 10%, transparent);\n        }\n      }\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:focus\\:text-destructive {\n    &[data-variant=\"destructive\"] {\n      &:focus {\n        color: var(--destructive);\n      }\n    }\n  }\n  .data-\\[variant\\=outline\\]\\:border-l-0 {\n    &[data-variant=\"outline\"] {\n      border-left-style: var(--tw-border-style);\n      border-left-width: 0px;\n    }\n  }\n  .data-\\[variant\\=outline\\]\\:shadow-xs {\n    &[data-variant=\"outline\"] {\n      --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .data-\\[variant\\=outline\\]\\:first\\:border-l {\n    &[data-variant=\"outline\"] {\n      &:first-child {\n        border-left-style: var(--tw-border-style);\n        border-left-width: 1px;\n      }\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:inset-x-0 {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      inset-inline: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:bottom-0 {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      bottom: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:mt-24 {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      margin-top: calc(var(--spacing) * 24);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:max-h-\\[80vh\\] {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      max-height: 80vh;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:rounded-t-lg {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      border-top-left-radius: var(--radius);\n      border-top-right-radius: var(--radius);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:border-t {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      border-top-style: var(--tw-border-style);\n      border-top-width: 1px;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=left\\]\\:inset-y-0 {\n    &[data-vaul-drawer-direction=\"left\"] {\n      inset-block: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=left\\]\\:left-0 {\n    &[data-vaul-drawer-direction=\"left\"] {\n      left: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=left\\]\\:w-3\\/4 {\n    &[data-vaul-drawer-direction=\"left\"] {\n      width: calc(3/4 * 100%);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=left\\]\\:border-r {\n    &[data-vaul-drawer-direction=\"left\"] {\n      border-right-style: var(--tw-border-style);\n      border-right-width: 1px;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=right\\]\\:inset-y-0 {\n    &[data-vaul-drawer-direction=\"right\"] {\n      inset-block: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=right\\]\\:right-0 {\n    &[data-vaul-drawer-direction=\"right\"] {\n      right: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=right\\]\\:w-3\\/4 {\n    &[data-vaul-drawer-direction=\"right\"] {\n      width: calc(3/4 * 100%);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=right\\]\\:border-l {\n    &[data-vaul-drawer-direction=\"right\"] {\n      border-left-style: var(--tw-border-style);\n      border-left-width: 1px;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:inset-x-0 {\n    &[data-vaul-drawer-direction=\"top\"] {\n      inset-inline: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:top-0 {\n    &[data-vaul-drawer-direction=\"top\"] {\n      top: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:mb-24 {\n    &[data-vaul-drawer-direction=\"top\"] {\n      margin-bottom: calc(var(--spacing) * 24);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:max-h-\\[80vh\\] {\n    &[data-vaul-drawer-direction=\"top\"] {\n      max-height: 80vh;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:rounded-b-lg {\n    &[data-vaul-drawer-direction=\"top\"] {\n      border-bottom-right-radius: var(--radius);\n      border-bottom-left-radius: var(--radius);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:border-b {\n    &[data-vaul-drawer-direction=\"top\"] {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 1px;\n    }\n  }\n  .sm\\:order-first {\n    @media (width >= 40rem) {\n      order: -9999;\n    }\n  }\n  .sm\\:mt-0 {\n    @media (width >= 40rem) {\n      margin-top: calc(var(--spacing) * 0);\n    }\n  }\n  .sm\\:flex {\n    @media (width >= 40rem) {\n      display: flex;\n    }\n  }\n  .sm\\:inline {\n    @media (width >= 40rem) {\n      display: inline;\n    }\n  }\n  .sm\\:w-auto {\n    @media (width >= 40rem) {\n      width: auto;\n    }\n  }\n  .sm\\:w-full {\n    @media (width >= 40rem) {\n      width: 100%;\n    }\n  }\n  .sm\\:max-w-\\[600px\\] {\n    @media (width >= 40rem) {\n      max-width: 600px;\n    }\n  }\n  .sm\\:max-w-lg {\n    @media (width >= 40rem) {\n      max-width: var(--container-lg);\n    }\n  }\n  .sm\\:max-w-md {\n    @media (width >= 40rem) {\n      max-width: var(--container-md);\n    }\n  }\n  .sm\\:max-w-sm {\n    @media (width >= 40rem) {\n      max-width: var(--container-sm);\n    }\n  }\n  .sm\\:max-w-xs {\n    @media (width >= 40rem) {\n      max-width: var(--container-xs);\n    }\n  }\n  .sm\\:grid-cols-2 {\n    @media (width >= 40rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .sm\\:flex-row {\n    @media (width >= 40rem) {\n      flex-direction: row;\n    }\n  }\n  .sm\\:justify-between {\n    @media (width >= 40rem) {\n      justify-content: space-between;\n    }\n  }\n  .sm\\:justify-end {\n    @media (width >= 40rem) {\n      justify-content: flex-end;\n    }\n  }\n  .sm\\:justify-start {\n    @media (width >= 40rem) {\n      justify-content: flex-start;\n    }\n  }\n  .sm\\:gap-0 {\n    @media (width >= 40rem) {\n      gap: calc(var(--spacing) * 0);\n    }\n  }\n  .sm\\:gap-2\\.5 {\n    @media (width >= 40rem) {\n      gap: calc(var(--spacing) * 2.5);\n    }\n  }\n  .sm\\:px-6 {\n    @media (width >= 40rem) {\n      padding-inline: calc(var(--spacing) * 6);\n    }\n  }\n  .sm\\:px-24 {\n    @media (width >= 40rem) {\n      padding-inline: calc(var(--spacing) * 24);\n    }\n  }\n  .sm\\:text-center {\n    @media (width >= 40rem) {\n      text-align: center;\n    }\n  }\n  .sm\\:text-left {\n    @media (width >= 40rem) {\n      text-align: left;\n    }\n  }\n  .sm\\:text-4xl {\n    @media (width >= 40rem) {\n      font-size: var(--text-4xl);\n      line-height: var(--tw-leading, var(--text-4xl--line-height));\n    }\n  }\n  .sm\\:text-5xl {\n    @media (width >= 40rem) {\n      font-size: var(--text-5xl);\n      line-height: var(--tw-leading, var(--text-5xl--line-height));\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=left\\]\\:sm\\:max-w-sm {\n    &[data-vaul-drawer-direction=\"left\"] {\n      @media (width >= 40rem) {\n        max-width: var(--container-sm);\n      }\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=right\\]\\:sm\\:max-w-sm {\n    &[data-vaul-drawer-direction=\"right\"] {\n      @media (width >= 40rem) {\n        max-width: var(--container-sm);\n      }\n    }\n  }\n  .md\\:mb-6 {\n    @media (width >= 48rem) {\n      margin-bottom: calc(var(--spacing) * 6);\n    }\n  }\n  .md\\:block {\n    @media (width >= 48rem) {\n      display: block;\n    }\n  }\n  .md\\:flex {\n    @media (width >= 48rem) {\n      display: flex;\n    }\n  }\n  .md\\:hidden {\n    @media (width >= 48rem) {\n      display: none;\n    }\n  }\n  .md\\:w-\\[30\\%\\] {\n    @media (width >= 48rem) {\n      width: 30%;\n    }\n  }\n  .md\\:columns-2 {\n    @media (width >= 48rem) {\n      columns: 2;\n    }\n  }\n  .md\\:grid-cols-2 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-3 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-4 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .md\\:flex-row {\n    @media (width >= 48rem) {\n      flex-direction: row;\n    }\n  }\n  .md\\:items-center {\n    @media (width >= 48rem) {\n      align-items: center;\n    }\n  }\n  .md\\:gap-1\\.5 {\n    @media (width >= 48rem) {\n      gap: calc(var(--spacing) * 1.5);\n    }\n  }\n  .md\\:gap-6 {\n    @media (width >= 48rem) {\n      gap: calc(var(--spacing) * 6);\n    }\n  }\n  .md\\:gap-8 {\n    @media (width >= 48rem) {\n      gap: calc(var(--spacing) * 8);\n    }\n  }\n  .md\\:space-x-2 {\n    @media (width >= 48rem) {\n      :where(& > :not(:last-child)) {\n        --tw-space-x-reverse: 0;\n        margin-inline-start: calc(calc(var(--spacing) * 2) * var(--tw-space-x-reverse));\n        margin-inline-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-x-reverse)));\n      }\n    }\n  }\n  .md\\:p-8 {\n    @media (width >= 48rem) {\n      padding: calc(var(--spacing) * 8);\n    }\n  }\n  .md\\:px-6 {\n    @media (width >= 48rem) {\n      padding-inline: calc(var(--spacing) * 6);\n    }\n  }\n  .md\\:px-8 {\n    @media (width >= 48rem) {\n      padding-inline: calc(var(--spacing) * 8);\n    }\n  }\n  .md\\:px-16 {\n    @media (width >= 48rem) {\n      padding-inline: calc(var(--spacing) * 16);\n    }\n  }\n  .md\\:py-6 {\n    @media (width >= 48rem) {\n      padding-block: calc(var(--spacing) * 6);\n    }\n  }\n  .md\\:py-20 {\n    @media (width >= 48rem) {\n      padding-block: calc(var(--spacing) * 20);\n    }\n  }\n  .md\\:py-24 {\n    @media (width >= 48rem) {\n      padding-block: calc(var(--spacing) * 24);\n    }\n  }\n  .md\\:text-left {\n    @media (width >= 48rem) {\n      text-align: left;\n    }\n  }\n  .md\\:text-3xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-3xl);\n      line-height: var(--tw-leading, var(--text-3xl--line-height));\n    }\n  }\n  .md\\:text-5xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-5xl);\n      line-height: var(--tw-leading, var(--text-5xl--line-height));\n    }\n  }\n  .md\\:text-6xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-6xl);\n      line-height: var(--tw-leading, var(--text-6xl--line-height));\n    }\n  }\n  .md\\:text-7xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-7xl);\n      line-height: var(--tw-leading, var(--text-7xl--line-height));\n    }\n  }\n  .md\\:text-base {\n    @media (width >= 48rem) {\n      font-size: var(--text-base);\n      line-height: var(--tw-leading, var(--text-base--line-height));\n    }\n  }\n  .md\\:text-lg {\n    @media (width >= 48rem) {\n      font-size: var(--text-lg);\n      line-height: var(--tw-leading, var(--text-lg--line-height));\n    }\n  }\n  .md\\:text-sm {\n    @media (width >= 48rem) {\n      font-size: var(--text-sm);\n      line-height: var(--tw-leading, var(--text-sm--line-height));\n    }\n  }\n  .md\\:text-xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-xl);\n      line-height: var(--tw-leading, var(--text-xl--line-height));\n    }\n  }\n  .md\\:opacity-0 {\n    @media (width >= 48rem) {\n      opacity: 0%;\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:m-2 {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        margin: calc(var(--spacing) * 2);\n      }\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:ml-0 {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        margin-left: calc(var(--spacing) * 0);\n      }\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:rounded-xl {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        border-radius: calc(var(--radius) + 4px);\n      }\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:shadow-sm {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:peer-data-\\[state\\=collapsed\\]\\:ml-2 {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        &:is(:where(.peer)[data-state=\"collapsed\"] ~ *) {\n          margin-left: calc(var(--spacing) * 2);\n        }\n      }\n    }\n  }\n  .md\\:after\\:hidden {\n    @media (width >= 48rem) {\n      &::after {\n        content: var(--tw-content);\n        display: none;\n      }\n    }\n  }\n  .lg\\:order-last {\n    @media (width >= 64rem) {\n      order: 9999;\n    }\n  }\n  .lg\\:col-span-2 {\n    @media (width >= 64rem) {\n      grid-column: span 2 / span 2;\n    }\n  }\n  .lg\\:grid-cols-2 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-3 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-4 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-6 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(6, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-\\[1fr_400px\\] {\n    @media (width >= 64rem) {\n      grid-template-columns: 1fr 400px;\n    }\n  }\n  .lg\\:flex-row {\n    @media (width >= 64rem) {\n      flex-direction: row;\n    }\n  }\n  .lg\\:items-center {\n    @media (width >= 64rem) {\n      align-items: center;\n    }\n  }\n  .lg\\:gap-2 {\n    @media (width >= 64rem) {\n      gap: calc(var(--spacing) * 2);\n    }\n  }\n  .lg\\:gap-12 {\n    @media (width >= 64rem) {\n      gap: calc(var(--spacing) * 12);\n    }\n  }\n  .lg\\:space-y-0 {\n    @media (width >= 64rem) {\n      :where(& > :not(:last-child)) {\n        --tw-space-y-reverse: 0;\n        margin-block-start: calc(calc(var(--spacing) * 0) * var(--tw-space-y-reverse));\n        margin-block-end: calc(calc(var(--spacing) * 0) * calc(1 - var(--tw-space-y-reverse)));\n      }\n    }\n  }\n  .lg\\:space-x-4 {\n    @media (width >= 64rem) {\n      :where(& > :not(:last-child)) {\n        --tw-space-x-reverse: 0;\n        margin-inline-start: calc(calc(var(--spacing) * 4) * var(--tw-space-x-reverse));\n        margin-inline-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-x-reverse)));\n      }\n    }\n  }\n  .lg\\:px-6 {\n    @media (width >= 64rem) {\n      padding-inline: calc(var(--spacing) * 6);\n    }\n  }\n  .lg\\:px-8 {\n    @media (width >= 64rem) {\n      padding-inline: calc(var(--spacing) * 8);\n    }\n  }\n  .lg\\:py-20 {\n    @media (width >= 64rem) {\n      padding-block: calc(var(--spacing) * 20);\n    }\n  }\n  .lg\\:py-32 {\n    @media (width >= 64rem) {\n      padding-block: calc(var(--spacing) * 32);\n    }\n  }\n  .lg\\:pt-24 {\n    @media (width >= 64rem) {\n      padding-top: calc(var(--spacing) * 24);\n    }\n  }\n  .lg\\:text-left {\n    @media (width >= 64rem) {\n      text-align: left;\n    }\n  }\n  .lg\\:text-4xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-4xl);\n      line-height: var(--tw-leading, var(--text-4xl--line-height));\n    }\n  }\n  .lg\\:text-6xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-6xl);\n      line-height: var(--tw-leading, var(--text-6xl--line-height));\n    }\n  }\n  .xl\\:columns-3 {\n    @media (width >= 80rem) {\n      columns: 3;\n    }\n  }\n  .xl\\:grid-cols-\\[1fr_600px\\] {\n    @media (width >= 80rem) {\n      grid-template-columns: 1fr 600px;\n    }\n  }\n  .xl\\:text-6xl\\/none {\n    @media (width >= 80rem) {\n      font-size: var(--text-6xl);\n      line-height: 1;\n    }\n  }\n  .\\32 xl\\:columns-4 {\n    @media (width >= 96rem) {\n      columns: 4;\n    }\n  }\n  .\\@\\[250px\\]\\/card\\:text-3xl {\n    @container card (width >= 250px) {\n      font-size: var(--text-3xl);\n      line-height: var(--tw-leading, var(--text-3xl--line-height));\n    }\n  }\n  .\\@xl\\/main\\:grid-cols-2 {\n    @container main (width >= 36rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .\\@5xl\\/main\\:grid-cols-4 {\n    @container main (width >= 64rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .dark\\:border-blue-800\\/30 {\n    &:is(.dark *) {\n      border-color: color-mix(in srgb, oklch(42.4% 0.199 265.638) 30%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        border-color: color-mix(in oklab, var(--color-blue-800) 30%, transparent);\n      }\n    }\n  }\n  .dark\\:border-green-800\\/30 {\n    &:is(.dark *) {\n      border-color: color-mix(in srgb, oklch(44.8% 0.119 151.328) 30%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        border-color: color-mix(in oklab, var(--color-green-800) 30%, transparent);\n      }\n    }\n  }\n  .dark\\:border-green-900\\/30 {\n    &:is(.dark *) {\n      border-color: color-mix(in srgb, oklch(39.3% 0.095 152.535) 30%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        border-color: color-mix(in oklab, var(--color-green-900) 30%, transparent);\n      }\n    }\n  }\n  .dark\\:border-red-900\\/30 {\n    &:is(.dark *) {\n      border-color: color-mix(in srgb, oklch(39.6% 0.141 25.723) 30%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        border-color: color-mix(in oklab, var(--color-red-900) 30%, transparent);\n      }\n    }\n  }\n  .dark\\:border-white\\/10 {\n    &:is(.dark *) {\n      border-color: color-mix(in srgb, #fff 10%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        border-color: color-mix(in oklab, var(--color-white) 10%, transparent);\n      }\n    }\n  }\n  .dark\\:border-yellow-800\\/30 {\n    &:is(.dark *) {\n      border-color: color-mix(in srgb, oklch(47.6% 0.114 61.907) 30%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        border-color: color-mix(in oklab, var(--color-yellow-800) 30%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-background\\/40 {\n    &:is(.dark *) {\n      background-color: var(--background);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--background) 40%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-black\\/10 {\n    &:is(.dark *) {\n      background-color: color-mix(in srgb, #000 10%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-black) 10%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-green-900\\/20 {\n    &:is(.dark *) {\n      background-color: color-mix(in srgb, oklch(39.3% 0.095 152.535) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-green-900) 20%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-green-900\\/30 {\n    &:is(.dark *) {\n      background-color: color-mix(in srgb, oklch(39.3% 0.095 152.535) 30%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-green-900) 30%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-input\\/30 {\n    &:is(.dark *) {\n      background-color: var(--input);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--input) 30%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-neutral-900 {\n    &:is(.dark *) {\n      background-color: var(--color-neutral-900);\n    }\n  }\n  .dark\\:bg-neutral-950 {\n    &:is(.dark *) {\n      background-color: var(--color-neutral-950);\n    }\n  }\n  .dark\\:bg-neutral-950\\/80 {\n    &:is(.dark *) {\n      background-color: color-mix(in srgb, oklch(14.5% 0 0) 80%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-neutral-950) 80%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-red-900\\/20 {\n    &:is(.dark *) {\n      background-color: color-mix(in srgb, oklch(39.6% 0.141 25.723) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-red-900) 20%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-red-900\\/30 {\n    &:is(.dark *) {\n      background-color: color-mix(in srgb, oklch(39.6% 0.141 25.723) 30%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-red-900) 30%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-secondary\\/20 {\n    &:is(.dark *) {\n      background-color: var(--secondary);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--secondary) 20%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-white\\/5 {\n    &:is(.dark *) {\n      background-color: color-mix(in srgb, #fff 5%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-white) 5%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-white\\/10 {\n    &:is(.dark *) {\n      background-color: color-mix(in srgb, #fff 10%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-white) 10%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-yellow-900\\/30 {\n    &:is(.dark *) {\n      background-color: color-mix(in srgb, oklch(42.1% 0.095 57.708) 30%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-yellow-900) 30%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-gradient-to-b {\n    &:is(.dark *) {\n      --tw-gradient-position: to bottom in oklab;\n      background-image: linear-gradient(var(--tw-gradient-stops));\n    }\n  }\n  .dark\\:from-blue-400 {\n    &:is(.dark *) {\n      --tw-gradient-from: var(--color-blue-400);\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:from-blue-900\\/30 {\n    &:is(.dark *) {\n      --tw-gradient-from: color-mix(in srgb, oklch(37.9% 0.146 265.522) 30%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-from: color-mix(in oklab, var(--color-blue-900) 30%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:from-green-400 {\n    &:is(.dark *) {\n      --tw-gradient-from: var(--color-green-400);\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:from-green-900\\/10 {\n    &:is(.dark *) {\n      --tw-gradient-from: color-mix(in srgb, oklch(39.3% 0.095 152.535) 10%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-from: color-mix(in oklab, var(--color-green-900) 10%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:from-green-900\\/30 {\n    &:is(.dark *) {\n      --tw-gradient-from: color-mix(in srgb, oklch(39.3% 0.095 152.535) 30%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-from: color-mix(in oklab, var(--color-green-900) 30%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:from-green-900\\/40 {\n    &:is(.dark *) {\n      --tw-gradient-from: color-mix(in srgb, oklch(39.3% 0.095 152.535) 40%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-from: color-mix(in oklab, var(--color-green-900) 40%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:from-red-900\\/10 {\n    &:is(.dark *) {\n      --tw-gradient-from: color-mix(in srgb, oklch(39.6% 0.141 25.723) 10%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-from: color-mix(in oklab, var(--color-red-900) 10%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:from-red-900\\/40 {\n    &:is(.dark *) {\n      --tw-gradient-from: color-mix(in srgb, oklch(39.6% 0.141 25.723) 40%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-from: color-mix(in oklab, var(--color-red-900) 40%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:from-yellow-400 {\n    &:is(.dark *) {\n      --tw-gradient-from: var(--color-yellow-400);\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:from-yellow-900\\/30 {\n    &:is(.dark *) {\n      --tw-gradient-from: color-mix(in srgb, oklch(42.1% 0.095 57.708) 30%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-from: color-mix(in oklab, var(--color-yellow-900) 30%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:to-blue-600 {\n    &:is(.dark *) {\n      --tw-gradient-to: var(--color-blue-600);\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:to-blue-800\\/20 {\n    &:is(.dark *) {\n      --tw-gradient-to: color-mix(in srgb, oklch(42.4% 0.199 265.638) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-to: color-mix(in oklab, var(--color-blue-800) 20%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:to-green-600 {\n    &:is(.dark *) {\n      --tw-gradient-to: var(--color-green-600);\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:to-green-800\\/20 {\n    &:is(.dark *) {\n      --tw-gradient-to: color-mix(in srgb, oklch(44.8% 0.119 151.328) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-to: color-mix(in oklab, var(--color-green-800) 20%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:to-green-900\\/5 {\n    &:is(.dark *) {\n      --tw-gradient-to: color-mix(in srgb, oklch(39.3% 0.095 152.535) 5%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-to: color-mix(in oklab, var(--color-green-900) 5%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:to-green-900\\/20 {\n    &:is(.dark *) {\n      --tw-gradient-to: color-mix(in srgb, oklch(39.3% 0.095 152.535) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-to: color-mix(in oklab, var(--color-green-900) 20%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:to-red-900\\/5 {\n    &:is(.dark *) {\n      --tw-gradient-to: color-mix(in srgb, oklch(39.6% 0.141 25.723) 5%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-to: color-mix(in oklab, var(--color-red-900) 5%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:to-red-900\\/20 {\n    &:is(.dark *) {\n      --tw-gradient-to: color-mix(in srgb, oklch(39.6% 0.141 25.723) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-to: color-mix(in oklab, var(--color-red-900) 20%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:to-yellow-600 {\n    &:is(.dark *) {\n      --tw-gradient-to: var(--color-yellow-600);\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:to-yellow-800\\/20 {\n    &:is(.dark *) {\n      --tw-gradient-to: color-mix(in srgb, oklch(47.6% 0.114 61.907) 20%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-gradient-to: color-mix(in oklab, var(--color-yellow-800) 20%, transparent);\n      }\n      --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n    }\n  }\n  .dark\\:fill-blue-400 {\n    &:is(.dark *) {\n      fill: var(--color-blue-400);\n    }\n  }\n  .dark\\:fill-gray-300 {\n    &:is(.dark *) {\n      fill: var(--color-gray-300);\n    }\n  }\n  .dark\\:fill-gray-400 {\n    &:is(.dark *) {\n      fill: var(--color-gray-400);\n    }\n  }\n  .dark\\:fill-red-400 {\n    &:is(.dark *) {\n      fill: var(--color-red-400);\n    }\n  }\n  .dark\\:fill-white {\n    &:is(.dark *) {\n      fill: var(--color-white);\n    }\n  }\n  .dark\\:stroke-gray-400 {\n    &:is(.dark *) {\n      stroke: var(--color-gray-400);\n    }\n  }\n  .dark\\:stroke-purple-400 {\n    &:is(.dark *) {\n      stroke: var(--color-purple-400);\n    }\n  }\n  .dark\\:stroke-slate-400 {\n    &:is(.dark *) {\n      stroke: var(--color-slate-400);\n    }\n  }\n  .dark\\:stroke-white {\n    &:is(.dark *) {\n      stroke: var(--color-white);\n    }\n  }\n  .dark\\:text-green-400 {\n    &:is(.dark *) {\n      color: var(--color-green-400);\n    }\n  }\n  .dark\\:text-muted-foreground {\n    &:is(.dark *) {\n      color: var(--muted-foreground);\n    }\n  }\n  .dark\\:text-neutral-300 {\n    &:is(.dark *) {\n      color: var(--color-neutral-300);\n    }\n  }\n  .dark\\:text-red-400 {\n    &:is(.dark *) {\n      color: var(--color-red-400);\n    }\n  }\n  .dark\\:text-white {\n    &:is(.dark *) {\n      color: var(--color-white);\n    }\n  }\n  .dark\\:text-yellow-400 {\n    &:is(.dark *) {\n      color: var(--color-yellow-400);\n    }\n  }\n  .dark\\:opacity-30 {\n    &:is(.dark *) {\n      opacity: 30%;\n    }\n  }\n  .dark\\:shadow-2xl {\n    &:is(.dark *) {\n      --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .dark\\:\\[box-shadow\\:0_-20px_80px_-20px_\\#d478ff0f_inset\\] {\n    &:is(.dark *) {\n      box-shadow: 0 -20px 80px -20px #d478ff0f inset;\n    }\n  }\n  .dark\\:shadow-primary\\/20 {\n    &:is(.dark *) {\n      --tw-shadow-color: var(--primary);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--primary) 20%, transparent) var(--tw-shadow-alpha), transparent);\n      }\n    }\n  }\n  .dark\\:hover\\:bg-input\\/50 {\n    &:is(.dark *) {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--input);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--input) 50%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:aria-invalid\\:ring-destructive\\/40 {\n    &:is(.dark *) {\n      &[aria-invalid=\"true\"] {\n        --tw-ring-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:\\*\\:data-\\[slot\\=card\\]\\:bg-card {\n    &:is(.dark *) {\n      :is(& > *) {\n        &[data-slot=\"card\"] {\n          background-color: var(--card);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=active\\]\\:border-input {\n    &:is(.dark *) {\n      &[data-state=\"active\"] {\n        border-color: var(--input);\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=active\\]\\:bg-input\\/30 {\n    &:is(.dark *) {\n      &[data-state=\"active\"] {\n        background-color: var(--input);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--input) 30%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=active\\]\\:text-foreground {\n    &:is(.dark *) {\n      &[data-state=\"active\"] {\n        color: var(--foreground);\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=checked\\]\\:bg-primary {\n    &:is(.dark *) {\n      &[data-state=\"checked\"] {\n        background-color: var(--primary);\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=checked\\]\\:bg-primary-foreground {\n    &:is(.dark *) {\n      &[data-state=\"checked\"] {\n        background-color: var(--primary-foreground);\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=unchecked\\]\\:bg-foreground {\n    &:is(.dark *) {\n      &[data-state=\"unchecked\"] {\n        background-color: var(--foreground);\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=unchecked\\]\\:bg-input\\/80 {\n    &:is(.dark *) {\n      &[data-state=\"unchecked\"] {\n        background-color: var(--input);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--input) 80%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/20 {\n    &:is(.dark *) {\n      &[data-variant=\"destructive\"] {\n        &:focus {\n          background-color: var(--destructive);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .\\[\\&_\\.recharts-cartesian-axis-tick_text\\]\\:fill-muted-foreground {\n    & .recharts-cartesian-axis-tick text {\n      fill: var(--muted-foreground);\n    }\n  }\n  .\\[\\&_\\.recharts-cartesian-grid_line\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border\\/50 {\n    & .recharts-cartesian-grid line[stroke='#ccc'] {\n      stroke: var(--border);\n      @supports (color: color-mix(in lab, red, red)) {\n        stroke: color-mix(in oklab, var(--border) 50%, transparent);\n      }\n    }\n  }\n  .\\[\\&_\\.recharts-curve\\.recharts-tooltip-cursor\\]\\:stroke-border {\n    & .recharts-curve.recharts-tooltip-cursor {\n      stroke: var(--border);\n    }\n  }\n  .\\[\\&_\\.recharts-dot\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent {\n    & .recharts-dot[stroke='#fff'] {\n      stroke: transparent;\n    }\n  }\n  .\\[\\&_\\.recharts-layer\\]\\:outline-hidden {\n    & .recharts-layer {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .\\[\\&_\\.recharts-polar-grid_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border {\n    & .recharts-polar-grid [stroke='#ccc'] {\n      stroke: var(--border);\n    }\n  }\n  .\\[\\&_\\.recharts-radial-bar-background-sector\\]\\:fill-muted {\n    & .recharts-radial-bar-background-sector {\n      fill: var(--muted);\n    }\n  }\n  .\\[\\&_\\.recharts-rectangle\\.recharts-tooltip-cursor\\]\\:fill-muted {\n    & .recharts-rectangle.recharts-tooltip-cursor {\n      fill: var(--muted);\n    }\n  }\n  .\\[\\&_\\.recharts-reference-line_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border {\n    & .recharts-reference-line [stroke='#ccc'] {\n      stroke: var(--border);\n    }\n  }\n  .\\[\\&_\\.recharts-sector\\]\\:outline-hidden {\n    & .recharts-sector {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .\\[\\&_\\.recharts-sector\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent {\n    & .recharts-sector[stroke='#fff'] {\n      stroke: transparent;\n    }\n  }\n  .\\[\\&_\\.recharts-surface\\]\\:outline-hidden {\n    & .recharts-surface {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .\\[\\&_svg\\]\\:pointer-events-none {\n    & svg {\n      pointer-events: none;\n    }\n  }\n  .\\[\\&_svg\\]\\:size-4 {\n    & svg {\n      width: calc(var(--spacing) * 4);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\&_svg\\]\\:shrink-0 {\n    & svg {\n      flex-shrink: 0;\n    }\n  }\n  .\\[\\&_svg\\:not\\(\\[class\\*\\=\\'size-\\'\\]\\)\\]\\:size-4 {\n    & svg:not([class*='size-']) {\n      width: calc(var(--spacing) * 4);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\&_svg\\:not\\(\\[class\\*\\=\\'text-\\'\\]\\)\\]\\:text-muted-foreground {\n    & svg:not([class*='text-']) {\n      color: var(--muted-foreground);\n    }\n  }\n  .\\[\\&_tr\\]\\:border-b {\n    & tr {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 1px;\n    }\n  }\n  .\\[\\&_tr\\:last-child\\]\\:border-0 {\n    & tr:last-child {\n      border-style: var(--tw-border-style);\n      border-width: 0px;\n    }\n  }\n  .\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0 {\n    &:has([role=checkbox]) {\n      padding-right: calc(var(--spacing) * 0);\n    }\n  }\n  .\\[\\.border-b\\]\\:pb-6 {\n    &:is(.border-b) {\n      padding-bottom: calc(var(--spacing) * 6);\n    }\n  }\n  .\\[\\.border-t\\]\\:pt-6 {\n    &:is(.border-t) {\n      padding-top: calc(var(--spacing) * 6);\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:flex {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          display: flex;\n        }\n      }\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:items-center {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          align-items: center;\n        }\n      }\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:gap-2 {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          gap: calc(var(--spacing) * 2);\n        }\n      }\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:\\*\\:\\[svg\\]\\:\\!text-destructive {\n    &[data-variant=\"destructive\"] {\n      :is(& > *) {\n        &:is(svg) {\n          color: var(--destructive) !important;\n        }\n      }\n    }\n  }\n  .\\[\\&\\>\\[role\\=checkbox\\]\\]\\:translate-y-\\[2px\\] {\n    &>[role=checkbox] {\n      --tw-translate-y: 2px;\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .\\[\\&\\>button\\]\\:hidden {\n    &>button {\n      display: none;\n    }\n  }\n  .\\[\\&\\>span\\:last-child\\]\\:truncate {\n    &>span:last-child {\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:size-3\\.5 {\n    &>svg {\n      width: calc(var(--spacing) * 3.5);\n      height: calc(var(--spacing) * 3.5);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:size-4 {\n    &>svg {\n      width: calc(var(--spacing) * 4);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:h-2\\.5 {\n    &>svg {\n      height: calc(var(--spacing) * 2.5);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:h-3 {\n    &>svg {\n      height: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:w-2\\.5 {\n    &>svg {\n      width: calc(var(--spacing) * 2.5);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:w-3 {\n    &>svg {\n      width: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:shrink-0 {\n    &>svg {\n      flex-shrink: 0;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:text-muted-foreground {\n    &>svg {\n      color: var(--muted-foreground);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:text-sidebar-accent-foreground {\n    &>svg {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .\\[\\&\\>tr\\]\\:last\\:border-b-0 {\n    &>tr {\n      &:last-child {\n        border-bottom-style: var(--tw-border-style);\n        border-bottom-width: 0px;\n      }\n    }\n  }\n  .\\[\\&\\[data-state\\=open\\]\\>svg\\]\\:rotate-180 {\n    &[data-state=open]>svg {\n      rotate: 180deg;\n    }\n  }\n  .\\[\\[data-side\\=left\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-right-2 {\n    [data-side=left][data-collapsible=offcanvas] & {\n      right: calc(var(--spacing) * -2);\n    }\n  }\n  .\\[\\[data-side\\=left\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-e-resize {\n    [data-side=left][data-state=collapsed] & {\n      cursor: e-resize;\n    }\n  }\n  .\\[\\[data-side\\=right\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-left-2 {\n    [data-side=right][data-collapsible=offcanvas] & {\n      left: calc(var(--spacing) * -2);\n    }\n  }\n  .\\[\\[data-side\\=right\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-w-resize {\n    [data-side=right][data-state=collapsed] & {\n      cursor: w-resize;\n    }\n  }\n}\n:root {\n  --radius: 0.65rem;\n  --background: oklch(1 0 0);\n  --foreground: oklch(0.141 0.005 285.823);\n  --card: oklch(1 0 0);\n  --card-foreground: oklch(0.141 0.005 285.823);\n  --popover: oklch(1 0 0);\n  --popover-foreground: oklch(0.141 0.005 285.823);\n  --primary: oklch(0.606 0.25 292.717);\n  --primary-foreground: oklch(0.969 0.016 293.756);\n  --secondary: oklch(0.967 0.001 286.375);\n  --secondary-foreground: oklch(0.21 0.006 285.885);\n  --muted: oklch(0.967 0.001 286.375);\n  --muted-foreground: oklch(0.552 0.016 285.938);\n  --accent: oklch(0.967 0.001 286.375);\n  --accent-foreground: oklch(0.21 0.006 285.885);\n  --destructive: oklch(0.577 0.245 27.325);\n  --border: oklch(0.92 0.004 286.32);\n  --input: oklch(0.92 0.004 286.32);\n  --ring: oklch(0.606 0.25 292.717);\n  --chart-1: oklch(0.646 0.222 41.116);\n  --chart-2: oklch(0.6 0.118 184.704);\n  --chart-3: oklch(0.398 0.07 227.392);\n  --chart-4: oklch(0.828 0.189 84.429);\n  --chart-5: oklch(0.769 0.188 70.08);\n  --sidebar: oklch(0.985 0 0);\n  --sidebar-foreground: oklch(0.141 0.005 285.823);\n  --sidebar-primary: oklch(0.606 0.25 292.717);\n  --sidebar-primary-foreground: oklch(0.969 0.016 293.756);\n  --sidebar-accent: oklch(0.967 0.001 286.375);\n  --sidebar-accent-foreground: oklch(0.21 0.006 285.885);\n  --sidebar-border: oklch(0.92 0.004 286.32);\n  --sidebar-ring: oklch(0.606 0.25 292.717);\n}\n.dark {\n  --background: oklch(0.141 0.005 285.823);\n  --foreground: oklch(0.985 0 0);\n  --card: oklch(0.21 0.006 285.885);\n  --card-foreground: oklch(0.985 0 0);\n  --popover: oklch(0.21 0.006 285.885);\n  --popover-foreground: oklch(0.985 0 0);\n  --primary: oklch(0.541 0.281 293.009);\n  --primary-foreground: oklch(0.969 0.016 293.756);\n  --secondary: oklch(0.274 0.006 286.033);\n  --secondary-foreground: oklch(0.985 0 0);\n  --muted: oklch(0.274 0.006 286.033);\n  --muted-foreground: oklch(0.705 0.015 286.067);\n  --accent: oklch(0.274 0.006 286.033);\n  --accent-foreground: oklch(0.985 0 0);\n  --destructive: oklch(0.704 0.191 22.216);\n  --border: oklch(1 0 0 / 10%);\n  --input: oklch(1 0 0 / 15%);\n  --ring: oklch(0.541 0.281 293.009);\n  --chart-1: oklch(0.488 0.243 264.376);\n  --chart-2: oklch(0.696 0.17 162.48);\n  --chart-3: oklch(0.769 0.188 70.08);\n  --chart-4: oklch(0.627 0.265 303.9);\n  --chart-5: oklch(0.645 0.246 16.439);\n  --sidebar: oklch(0.21 0.006 285.885);\n  --sidebar-foreground: oklch(0.985 0 0);\n  --sidebar-primary: oklch(0.541 0.281 293.009);\n  --sidebar-primary-foreground: oklch(0.969 0.016 293.756);\n  --sidebar-accent: oklch(0.274 0.006 286.033);\n  --sidebar-accent-foreground: oklch(0.985 0 0);\n  --sidebar-border: oklch(1 0 0 / 10%);\n  --sidebar-ring: oklch(0.541 0.281 293.009);\n}\n@layer base {\n  * {\n    border-color: var(--border);\n    outline-color: var(--ring);\n    @supports (color: color-mix(in lab, red, red)) {\n      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);\n    }\n  }\n  body {\n    background-color: var(--background);\n    color: var(--foreground);\n  }\n}\n@keyframes marquee {\n  from {\n    transform: translateX(0);\n  }\n  to {\n    transform: translateX(calc(-100% - var(--gap)));\n  }\n}\n@keyframes marquee-vertical {\n  from {\n    transform: translateY(0);\n  }\n  to {\n    transform: translateY(calc(-100% - var(--gap)));\n  }\n}\n.animate-marquee {\n  animation: marquee var(--duration) linear infinite;\n}\n.animate-marquee-vertical {\n  animation: marquee-vertical var(--duration) linear infinite;\n}\ncanvas#neuro {\n  position: fixed;\n  inset: 0;\n  width: 100vw;\n  height: 100vh;\n  pointer-events: none;\n  opacity: 0.95;\n  z-index: 0;\n}\n.glass-effect {\n  backdrop-filter: blur(14px) brightness(0.91);\n  -webkit-backdrop-filter: blur(14px) brightness(0.91);\n}\n.floating {\n  animation: float 6s ease-in-out infinite;\n}\n.floating-delay {\n  animation: float 6s ease-in-out infinite;\n  animation-delay: 2s;\n}\n@keyframes float {\n  0% {\n    transform: translateY(0px);\n  }\n  50% {\n    transform: translateY(-20px);\n  }\n  100% {\n    transform: translateY(0px);\n  }\n}\n.glass {\n  background: rgba(255, 255, 255, 0.05);\n  backdrop-filter: blur(20px);\n  -webkit-backdrop-filter: blur(20px);\n}\n.connector {\n  stroke-dasharray: 8;\n  animation: dataStream 2s linear infinite;\n}\n.table-float {\n  animation: float 6s ease-in-out infinite;\n}\n.table-float:nth-child(2) {\n  animation-delay: -1s;\n}\n.table-float:nth-child(3) {\n  animation-delay: -2s;\n}\n.table-float:nth-child(4) {\n  animation-delay: -3s;\n}\n.gradient-border {\n  position: relative;\n  background: linear-gradient(\r\n    135deg,\r\n    rgba(179, 70, 229, 0.1),\r\n    rgba(212, 59, 246, 0.1),\r\n    rgba(139, 92, 246, 0.1)\r\n  );\n}\n.gradient-border::before {\n  content: \"\";\n  position: absolute;\n  inset: 0;\n  padding: 2px;\n  background: linear-gradient(135deg, #d546e5, #8c3bf6, #8b5cf6, #f50bc2);\n  border-radius: inherit;\n  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);\n  mask-composite: xor;\n  -webkit-mask-composite: xor;\n}\n.inner-glow {\n  box-shadow: inset 0 0 20px rgba(165, 70, 229, 0.3),\r\n    inset 0 0 40px rgba(199, 59, 246, 0.2), 0 0 30px rgba(139, 92, 246, 0.4);\n}\n.card-border {\n  background: rgba(79, 70, 229, 0.08);\n  border: 1px solid rgba(144, 70, 229, 0.3);\n  backdrop-filter: blur(20px);\n  -webkit-backdrop-filter: blur(20px);\n  box-shadow: 0 0 0 1px rgba(179, 70, 229, 0.3),\r\n    inset 0 0 30px rgba(147, 70, 229, 0.1),\r\n    inset 0 0 60px rgba(199, 59, 246, 0.05), 0 0 50px rgba(139, 92, 246, 0.2);\n}\n.dark .neural-bg {\n  background-image: linear-gradient(\r\n      90deg,\r\n      rgba(255, 255, 255, 0.08) 1px,\r\n      transparent 1px\r\n    ),\r\n    linear-gradient(rgba(255, 255, 255, 0.08) 1px, transparent 1px) !important;\n}\nbutton[style*=\"position: absolute;\"][style*=\"right: 0.6rem;\"][style*=\"top: 0.6rem;\"][style*=\"background-color: transparent;\"] {\n  display: none !important;\n}\n.purple-gradient {\n  background: radial-gradient(circle, rgba(248, 249, 250, 1) 0, rgba(240, 242, 247, 1) 86%, rgba(156, 59, 258, .3) 100%);\n}\n.dark .purple-gradient {\n  background: radial-gradient(circle, rgba(23, 23, 23, 0) 0, rgba(20, 17, 10, .2) 86%, rgba(104, 27, 289, .52) 100%);\n}\n@property --tw-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-scale-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-rotate-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-z {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-space-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-space-x-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-gradient-position {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-via {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-to {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-via-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 0%;\n}\n@property --tw-gradient-via-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 50%;\n}\n@property --tw-gradient-to-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-leading {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-font-weight {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-tracking {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ordinal {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-slashed-zero {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-numeric-figure {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-numeric-spacing {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-numeric-fraction {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-outline-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-drop-shadow-size {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ease {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-content {\n  syntax: \"*\";\n  initial-value: \"\";\n  inherits: false;\n}\n@keyframes spin {\n  to {\n    transform: rotate(360deg);\n  }\n}\n@keyframes ping {\n  75%, 100% {\n    transform: scale(2);\n    opacity: 0;\n  }\n}\n@keyframes pulse {\n  50% {\n    opacity: 0.5;\n  }\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-translate-x: 0;\n      --tw-translate-y: 0;\n      --tw-translate-z: 0;\n      --tw-scale-x: 1;\n      --tw-scale-y: 1;\n      --tw-scale-z: 1;\n      --tw-rotate-x: initial;\n      --tw-rotate-y: initial;\n      --tw-rotate-z: initial;\n      --tw-skew-x: initial;\n      --tw-skew-y: initial;\n      --tw-space-y-reverse: 0;\n      --tw-space-x-reverse: 0;\n      --tw-border-style: solid;\n      --tw-gradient-position: initial;\n      --tw-gradient-from: #0000;\n      --tw-gradient-via: #0000;\n      --tw-gradient-to: #0000;\n      --tw-gradient-stops: initial;\n      --tw-gradient-via-stops: initial;\n      --tw-gradient-from-position: 0%;\n      --tw-gradient-via-position: 50%;\n      --tw-gradient-to-position: 100%;\n      --tw-leading: initial;\n      --tw-font-weight: initial;\n      --tw-tracking: initial;\n      --tw-ordinal: initial;\n      --tw-slashed-zero: initial;\n      --tw-numeric-figure: initial;\n      --tw-numeric-spacing: initial;\n      --tw-numeric-fraction: initial;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n      --tw-outline-style: solid;\n      --tw-blur: initial;\n      --tw-brightness: initial;\n      --tw-contrast: initial;\n      --tw-grayscale: initial;\n      --tw-hue-rotate: initial;\n      --tw-invert: initial;\n      --tw-opacity: initial;\n      --tw-saturate: initial;\n      --tw-sepia: initial;\n      --tw-drop-shadow: initial;\n      --tw-drop-shadow-color: initial;\n      --tw-drop-shadow-alpha: 100%;\n      --tw-drop-shadow-size: initial;\n      --tw-backdrop-blur: initial;\n      --tw-backdrop-brightness: initial;\n      --tw-backdrop-contrast: initial;\n      --tw-backdrop-grayscale: initial;\n      --tw-backdrop-hue-rotate: initial;\n      --tw-backdrop-invert: initial;\n      --tw-backdrop-opacity: initial;\n      --tw-backdrop-saturate: initial;\n      --tw-backdrop-sepia: initial;\n      --tw-duration: initial;\n      --tw-ease: initial;\n      --tw-content: \"\";\n    }\n  }\n}\r\n"], "names": [], "mappings": "AACA;EA2lNE;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA3lNJ;EAEE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFF;EAyJE;;;;;;;EAMA;;;;;;;;;;EASA;;;;;;EAKA;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAq2LA;;;;;EAGE;IAAgD;;;;;EAIlD;;;;;;AAjpMF;;AAAA;EA0SE;;;;EAIA;;;;EAIA;;;;EAIA;;;;EAGA;;;;EAGA;;;;;;;;;;;;EAWA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAI3B;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAMF;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAiE;;;;;EAGhD;;;;EAEnB;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;;;EAKA;;;;EAEE;IAAgD;;;;;EAGxB;;;;;EAG1B;;;;;;EAKA;;;;;;EAKA;;;;EAEE;IAAgD;;;;;EAGxB;;;;;EAG1B;;;;;;EAKA;;;;EAEE;IAAgD;;;;;EAGxB;;;;;EAG1B;;;;EAEE;IAAgD;;;;;EAGxB;;;;;EAG1B;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;;EAIA;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAEtB;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;;EAIA;;;;EAGA;;;;;EAGE;IAAgC;;;;;;EAKlC;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;EAME;IAAuB;;;;;;;;EAUvB;IAAuB;;;;;;;;EAUvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAMzB;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAME;IAAuB;;;;;EAMzB;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAQA;;;;EAQA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAOE;;;;;EAOF;;;;;EAMA;;;;;EAMA;;;;;EAOE;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;;;EAUvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;IAG5B;;;;;EAMtB;IAAuB;;;;IAErB;MAAgD;;;;;IAG5B;;;;;EAMtB;IAAuB;;;;IAErB;MAAgD;;;;;IAG5B;;;;;EAMtB;IAAuB;;;;IAErB;MAAgD;;;;;IAG5B;;;;;EAMtB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IACE;;;;;EAQF;IACE;;;;;;EAQJ;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;;EAMA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;;;EAQA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAME;;;;;EAQA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAMtB;;;;;EAQA;;;;;EAQA;;;;;;;EAUA;;;;EAOA;;;;EAOA;;;;EAMF;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAItB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAItB;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAQI;IAAuB;;;;;EASvB;IAAuB;;;;;EAO3B;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAME;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAMF;;;;;EAMA;;;;;EAOE;;;;;EAOF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAOvB;IAAyB;;;;;EAOzB;IAAyB;;;;;EAM3B;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IACE;;;;;;;EAQF;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IACE;;;;;EAMF;IACE;;;;;EAMF;IACE;;;;;EAMF;IACE;;;;;;EAOF;IAEI;;;;;EAOJ;IACE;;;;;;EAOF;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IACE;;;;;;;EAQF;IACE;;;;;;;EAQF;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IAAkC;;;;;;EAMlC;IAAkC;;;;;EAKlC;IAAkC;;;;;EAKlC;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;;EAMA;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAItB;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAItB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAItB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAItB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAItB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAItB;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAItB;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAItB;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAItB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAItB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAItB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAItB;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAItB;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAG5B;;;;EAItB;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAQ9C;IAAuB;;;;IAErB;MAAgD;;;;;;EASpD;;;;EAEE;IAAgD;;;;;EAShD;;;;EAQF;;;;EAOA;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAEE;IAAgD;;;;;EAShD;;;;EAEE;IAAgD;;;;;EAQtD;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;EAKA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAOI;;;;EASA;;;;EASA;;;;EASA;;;;EAOJ;;;;;EAMA;;;;EAKA;;;;;;EAOA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;;EAOF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;AAKJ;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAkCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8CA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;AAGA;;;;AAGA;;;;;;;;;;AASA;;;;AAIA;;;;AAGA;;;;AAIA;;;;;;;;;;;;;;AAWA;;;;;AAKA;;;;;AAIA;;;;AAGA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;AASA;;;;;;;;;;;;AAWA;;;;AAIA;;;;;;;AASA;;;;AAQA;;;;AAGA;;;;AAGA;;;;AAGA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;;AAMA"}}]}