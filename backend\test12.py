#!/usr/bin/env python3
"""
Enhanced test script for ClarifAI Backend API with Cloudinary Integration
Tests all endpoints including render jobs with entry_id for Cloudinary upload and database updates.
"""

import asyncio
import httpx
import json
import time
import uuid
from typing import Optional, Dict, Any
from datetime import datetime

# Configuration
BASE_URL = "http://localhost:8000"
TIMEOUT = 30.0

# Sample Manim scripts for testing
SIMPLE_SCRIPT = """
from manim import *

class CloudinaryTestScene(Scene):
    def construct(self):
        title = Text("Cloudinary Upload Test", font_size=48)
        subtitle = Text("Testing enhanced workflow", font_size=24)
        subtitle.next_to(title, DOWN, buff=0.5)

        self.play(Write(title))
        self.wait(0.5)
        self.play(Write(subtitle))
        self.wait(1)
        self.play(FadeOut(title), FadeOut(subtitle))
"""

COMPLEX_SCRIPT = """
from manim import *

class EnhancedComplexScene(Scene):
    def construct(self):
        # Create geometric shapes
        circle = Circle(radius=1, color=BLUE)
        square = Square(side_length=2, color=RED)
        triangle = Triangle(color=GREEN)

        # Position them
        circle.shift(LEFT * 3)
        square.shift(ORIGIN)
        triangle.shift(RIGHT * 3)

        # Animate creation
        self.play(Create(circle), Create(square), Create(triangle))
        self.wait(0.5)

        # Transform sequence
        self.play(Transform(circle, square))
        self.wait(0.5)
        self.play(Transform(square, triangle))
        self.wait(1)

        # Add title with entry ID info
        title = Text("Enhanced Workflow Complete!", font_size=36)
        title.shift(UP * 2)
        self.play(Write(title))
        self.wait(1)
"""

MATH_SCRIPT = """
from manim import *

class CloudinaryMathScene(Scene):
    def construct(self):
        # Create mathematical equations
        equation1 = MathTex(r"e^{i\pi} + 1 = 0")
        equation2 = MathTex(r"\int_{-\infty}^{\infty} e^{-x^2} dx = \sqrt{\pi}")
        equation3 = MathTex(r"F(s) = \int_{0}^{\infty} f(t) e^{-st} dt")

        equations = VGroup(equation1, equation2, equation3)
        equations.arrange(DOWN, buff=0.8)
        equations.scale(1.2)

        # Animate each equation
        for eq in equations:
            self.play(Write(eq))
            self.wait(1)

        # Highlight all
        self.play(equations.animate.set_color(YELLOW))
        self.wait(1)
"""


class EnhancedBackendTester:
    """Enhanced backend API tester with Cloudinary integration tests."""

    def __init__(self, base_url: str = BASE_URL):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=TIMEOUT)
        self.test_results = []
        self.test_entry_ids = []  # Track generated entry IDs

    async def __aenter__(self):
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        await self.client.aclose()

    def generate_test_entry_id(self) -> str:
        """Generate a unique test entry ID."""
        entry_id = f"test-entry-{uuid.uuid4().hex[:8]}"
        self.test_entry_ids.append(entry_id)
        return entry_id

    def log_test(self, test_name: str, success: bool, details: str = ""):
        """Log test result."""
        status = "✅ PASS" if success else "❌ FAIL"
        timestamp = datetime.now().strftime("%H:%M:%S")
        message = f"[{timestamp}] {status} {test_name}"
        if details:
            message += f" - {details}"
        print(message)

        self.test_results.append(
            {
                "test": test_name,
                "success": success,
                "details": details,
                "timestamp": timestamp,
            }
        )

    async def test_health_check(self) -> bool:
        """Test basic health check."""
        try:
            response = await self.client.get(f"{self.base_url}/")
            response.raise_for_status()
            data = response.json()

            success = "message" in data
            self.log_test("Health Check", success, f"Status: {response.status_code}")
            return success

        except Exception as e:
            self.log_test("Health Check", False, str(e))
            return False

    async def test_single_render_with_entry_id(self) -> Optional[str]:
        """Test single render job with entry_id for Cloudinary upload."""
        try:
            entry_id = self.generate_test_entry_id()
            payload = {
                "script": SIMPLE_SCRIPT,
                "scene_name": "CloudinaryTestScene",
                "entry_id": entry_id,
                "priority": 1,
            }

            response = await self.client.post(f"{self.base_url}/render", json=payload)
            response.raise_for_status()
            data = response.json()

            success = "job_id" in data and "status" in data
            job_id = data.get("job_id") if success else None

            self.log_test(
                "Single Render with Entry ID",
                success,
                f"Job ID: {job_id}, Entry ID: {entry_id}, Status: {data.get('status')}",
            )
            return job_id

        except Exception as e:
            self.log_test("Single Render with Entry ID", False, str(e))
            return None

    async def test_batch_render_with_entry_id(self) -> Optional[str]:
        """Test batch render job with entry_id for Cloudinary upload."""
        try:
            entry_id = self.generate_test_entry_id()
            individual_entry_id = self.generate_test_entry_id()

            payload = {
                "scripts": [
                    {
                        "script": SIMPLE_SCRIPT,
                        "scene_name": "CloudinaryTestScene",
                        "entry_id": individual_entry_id,
                    },
                    {"script": MATH_SCRIPT, "scene_name": "CloudinaryMathScene"},
                    {"script": COMPLEX_SCRIPT, "scene_name": "EnhancedComplexScene"},
                ],
                "entry_id": entry_id,  # Global entry ID for merged video
                "priority": 2,
            }

            response = await self.client.post(
                f"{self.base_url}/batch_render", json=payload
            )
            response.raise_for_status()
            data = response.json()

            success = "job_id" in data and "status" in data
            job_id = data.get("job_id") if success else None

            self.log_test(
                "Batch Render with Entry ID",
                success,
                f"Job ID: {job_id}, Entry ID: {entry_id}, Scripts: {len(payload['scripts'])}",
            )
            return job_id

        except Exception as e:
            self.log_test("Batch Render with Entry ID", False, str(e))
            return None

    async def test_legacy_render_without_entry_id(self) -> Optional[str]:
        """Test legacy render job without entry_id (backward compatibility)."""
        try:
            payload = {
                "script": SIMPLE_SCRIPT,
                "scene_name": "CloudinaryTestScene",
                "priority": 1,
            }

            response = await self.client.post(f"{self.base_url}/render", json=payload)
            response.raise_for_status()
            data = response.json()

            success = "job_id" in data and "status" in data
            job_id = data.get("job_id") if success else None

            self.log_test(
                "Legacy Render (No Entry ID)",
                success,
                f"Job ID: {job_id}, Status: {data.get('status')}",
            )
            return job_id

        except Exception as e:
            self.log_test("Legacy Render (No Entry ID)", False, str(e))
            return None

    async def test_mixed_batch_render(self) -> Optional[str]:
        """Test batch render with mixed entry_id scenarios."""
        try:
            entry_id_1 = self.generate_test_entry_id()
            entry_id_2 = self.generate_test_entry_id()

            payload = {
                "scripts": [
                    {
                        "script": SIMPLE_SCRIPT,
                        "scene_name": "CloudinaryTestScene",
                        "entry_id": entry_id_1,  # Has entry ID
                    },
                    {
                        "script": MATH_SCRIPT,
                        "scene_name": "CloudinaryMathScene",
                        # No entry ID
                    },
                    {
                        "script": COMPLEX_SCRIPT,
                        "scene_name": "EnhancedComplexScene",
                        "entry_id": entry_id_2,  # Has entry ID
                    },
                ],
                "priority": 1,
                # No global entry ID
            }

            response = await self.client.post(
                f"{self.base_url}/batch_render", json=payload
            )
            response.raise_for_status()
            data = response.json()

            success = "job_id" in data and "status" in data
            job_id = data.get("job_id") if success else None

            self.log_test(
                "Mixed Batch Render",
                success,
                f"Job ID: {job_id}, Mixed Entry IDs: {[entry_id_1, entry_id_2]}",
            )
            return job_id

        except Exception as e:
            self.log_test("Mixed Batch Render", False, str(e))
            return None

    async def test_job_status_enhanced(self, job_id: str) -> Dict[str, Any]:
        """Test enhanced job status retrieval with Cloudinary URLs."""
        try:
            response = await self.client.get(f"{self.base_url}/jobs/{job_id}")
            response.raise_for_status()
            data = response.json()

            required_fields = ["job_id", "job_type", "status", "created_at"]
            success = all(field in data for field in required_fields)

            # Check for enhanced output URLs (local + Cloudinary)
            result = data.get("result", {})
            output_urls = result.get("output_urls", [])

            cloudinary_urls = [url for url in output_urls if "cloudinary.com" in url]
            local_urls = [url for url in output_urls if url.startswith("/media/")]

            details = f"Status: {data.get('status')}, URLs: {len(output_urls)} total"
            if cloudinary_urls:
                details += f", {len(cloudinary_urls)} Cloudinary"
            if local_urls:
                details += f", {len(local_urls)} local"

            self.log_test(f"Enhanced Job Status ({job_id[:8]}...)", success, details)
            return data

        except Exception as e:
            self.log_test(f"Enhanced Job Status ({job_id[:8]}...)", False, str(e))
            return {}

    async def test_configuration_check(self) -> bool:
        """Test configuration endpoint for Cloudinary setup."""
        try:
            response = await self.client.get(f"{self.base_url}/debug/config")
            response.raise_for_status()
            data = response.json()

            # Check for Cloudinary-related configuration
            cloudinary_configured = any(
                key.startswith("cloudinary") for key in data.keys()
            )

            success = isinstance(data, dict) and len(data) > 0

            self.log_test(
                "Configuration Check",
                success,
                f"Cloudinary config present: {cloudinary_configured}",
            )
            return success

        except Exception as e:
            self.log_test("Configuration Check", False, str(e))
            return False

    async def monitor_enhanced_job(
        self, job_id: str, max_checks: int = 10, interval: int = 3
    ):
        """Monitor a job with enhanced output tracking."""
        print(f"\n🔍 Monitoring enhanced job {job_id[:8]}...")

        for i in range(max_checks):
            status_data = await self.test_job_status_enhanced(job_id)

            if not status_data:
                break

            status = status_data.get("status", "unknown")
            progress = status_data.get("progress_percentage")

            print(f"   Check {i+1}/{max_checks}: Status = {status}", end="")
            if progress is not None:
                print(f", Progress = {progress}%", end="")
            print()

            # Check if job is complete
            if status in ["completed", "failed", "cancelled"]:
                if status == "completed":
                    result = status_data.get("result", {})
                    output_urls = result.get("output_urls", [])
                    processing_time = result.get("processing_time_seconds", 0)

                    print(f"   ✅ Job completed in {processing_time}s")

                    # Categorize URLs
                    cloudinary_urls = [
                        url for url in output_urls if "cloudinary.com" in url
                    ]
                    local_urls = [
                        url for url in output_urls if url.startswith("/media/")
                    ]

                    if local_urls:
                        print(f"   📁 Local: {local_urls[0]}")
                    if cloudinary_urls:
                        print(f"   ☁️  Cloudinary: {cloudinary_urls[0]}")
                    if not output_urls:
                        print(f"   ⚠️  No output URLs found")

                else:
                    error_msg = status_data.get("error_message", "Unknown error")
                    print(f"   ❌ Job {status}: {error_msg}")
                break

            if i < max_checks - 1:
                await asyncio.sleep(interval)
        else:
            print(f"   ⚠️  Monitoring timeout after {max_checks} checks")

    async def test_invalid_entry_id_requests(self):
        """Test error handling with invalid entry_id scenarios."""
        test_cases = [
            {
                "name": "Empty Entry ID",
                "payload": {
                    "script": SIMPLE_SCRIPT,
                    "scene_name": "Test",
                    "entry_id": "",
                },
            },
            {
                "name": "Very Long Entry ID",
                "payload": {
                    "script": SIMPLE_SCRIPT,
                    "scene_name": "Test",
                    "entry_id": "x" * 1000,
                },
            },
            {
                "name": "Special Characters in Entry ID",
                "payload": {
                    "script": SIMPLE_SCRIPT,
                    "scene_name": "Test",
                    "entry_id": "test@#$%^&*()entry",
                },
            },
            {
                "name": "Null Entry ID",
                "payload": {
                    "script": SIMPLE_SCRIPT,
                    "scene_name": "Test",
                    "entry_id": None,
                },
            },
        ]

        for case in test_cases:
            try:
                response = await self.client.post(
                    f"{self.base_url}/render", json=case["payload"]
                )

                # These should either succeed (for valid entry IDs) or fail gracefully
                success = response.status_code in [200, 400, 422]

                self.log_test(
                    f"Entry ID Validation: {case['name']}",
                    success,
                    f"Status: {response.status_code}",
                )

            except Exception as e:
                # Exceptions are acceptable for invalid requests
                self.log_test(
                    f"Entry ID Validation: {case['name']}",
                    True,
                    f"Exception: {str(e)[:50]}",
                )

    def print_enhanced_summary(self):
        """Print enhanced test summary with entry ID tracking."""
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result["success"])
        failed_tests = total_tests - passed_tests

        print(f"\n{'='*70}")
        print(f"ENHANCED CLOUDINARY WORKFLOW TEST SUMMARY")
        print(f"{'='*70}")
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests} ✅")
        print(f"Failed: {failed_tests} ❌")
        print(
            f"Success Rate: {(passed_tests/total_tests)*100:.1f}%"
            if total_tests > 0
            else "N/A"
        )
        print(f"Generated Entry IDs: {len(self.test_entry_ids)}")

        if self.test_entry_ids:
            print(f"\nTest Entry IDs Created:")
            for i, entry_id in enumerate(self.test_entry_ids, 1):
                print(f"  {i}. {entry_id}")

        if failed_tests > 0:
            print(f"\nFailed Tests:")
            for result in self.test_results:
                if not result["success"]:
                    print(f"  - {result['test']}: {result['details']}")

        print(f"\n💡 Note: Entry IDs are test values. In production, these would be")
        print(f"   real database entry IDs from your frontend application.")


async def main():
    """Main enhanced test execution."""
    print("🚀 Starting Enhanced ClarifAI Backend API Tests with Cloudinary Integration")
    print(f"Server URL: {BASE_URL}")
    print(f"Timeout: {TIMEOUT}s")
    print("=" * 70)

    async with EnhancedBackendTester(BASE_URL) as tester:
        # Basic health check
        if not await tester.test_health_check():
            print("❌ Server is not responding. Please check if the server is running.")
            return

        # Test configuration
        print("\n⚙️  Testing Configuration...")
        await tester.test_configuration_check()

        # Test enhanced job submission
        print("\n📝 Testing Enhanced Job Submission with Entry IDs...")
        single_job_id = await tester.test_single_render_with_entry_id()
        batch_job_id = await tester.test_batch_render_with_entry_id()
        mixed_job_id = await tester.test_mixed_batch_render()

        # Test backward compatibility
        print("\n🔄 Testing Backward Compatibility...")
        legacy_job_id = await tester.test_legacy_render_without_entry_id()

        # Monitor enhanced jobs
        print("\n📊 Monitoring Enhanced Jobs...")
        jobs_to_monitor = [
            (single_job_id, "Single Render with Entry ID"),
            (batch_job_id, "Batch Render with Entry ID"),
            (mixed_job_id, "Mixed Batch Render"),
            (legacy_job_id, "Legacy Render"),
        ]

        for job_id, job_name in jobs_to_monitor:
            if job_id:
                print(f"\n🔍 {job_name}:")
                await tester.monitor_enhanced_job(job_id, max_checks=5, interval=2)

        # Test error handling with entry IDs
        print("\n🚨 Testing Entry ID Validation...")
        await tester.test_invalid_entry_id_requests()

        # Print enhanced summary
        tester.print_enhanced_summary()

        print(f"\n🎯 Enhanced Workflow Features Tested:")
        print(f"   ✅ Entry ID parameter support")
        print(f"   ✅ Cloudinary upload integration")
        print(f"   ✅ Database update workflow")
        print(f"   ✅ Backward compatibility")
        print(f"   ✅ Mixed scenario handling")
        print(f"   ✅ Error handling and validation")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n⚠️  Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Test execution failed: {e}")
