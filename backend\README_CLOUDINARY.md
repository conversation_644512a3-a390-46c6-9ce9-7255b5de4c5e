# Cloudinary Integration for Video Upload

This document describes the enhanced video rendering workflow that includes automatic upload to Cloudinary and database updates.

## Overview

The enhanced workflow provides:

1. **Video Rendering**: Manim scripts are rendered to MP4 videos
2. **Cloudinary Upload**: Completed videos are automatically uploaded to Cloudinary
3. **Database Update**: Entry records are updated with the Cloudinary video URL
4. **Error Handling**: Robust error handling with fallback to local storage

## Workflow

```
POST /render (with entryId) → Queue Job → Render Video → Upload to Cloudinary → Update Database
```

### 1. Submit Render Job with Entry ID

```bash
curl -X POST http://localhost:8000/render \
  -H "Content-Type: application/json" \
  -d '{
    "script": "from manim import *\nclass MyScene(Scene):\n    def construct(self):\n        text = Text(\"Hello World\")\n        self.play(Write(text))",
    "scene_name": "MyScene",
    "entry_id": "your-database-entry-id",
    "priority": 0
  }'
```

### 2. Video Processing

The system will:
- Render the Manim script to MP4
- Save the video locally in `/media/{job_id}/`
- Upload the video to Cloudinary (if configured)
- Update the database entry with the Cloudinary URL

### 3. Response

The job result will include both local and Cloudinary URLs:

```json
{
  "job_id": "abc123",
  "success": true,
  "output_urls": [
    "/media/abc123/MyScene.mp4",
    "https://res.cloudinary.com/your-cloud/video/upload/v123/clarifai-renders/renders/abc123/MyScene.mp4"
  ]
}
```

## Configuration

### Environment Variables

Add these to your `.env` file:

```bash
# Cloudinary Configuration
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret
CLOUDINARY_UPLOAD_PRESET=your_upload_preset  # Optional

# Backend API Configuration
BACKEND_API_BASE_URL=http://localhost:3000  # Your frontend API URL
```

### Cloudinary Setup

1. **Create Account**: Sign up at [Cloudinary](https://cloudinary.com)
2. **Get Credentials**: Find your cloud name, API key, and API secret in the dashboard
3. **Configure Upload Preset** (Optional): Create an upload preset for unsigned uploads

### Database API

The system expects a `/api/entries/update` endpoint that accepts:

```json
{
  "entryId": "string",
  "videoURL": "string"
}
```

## API Endpoints

### Enhanced Render Endpoint

**POST** `/render`

```json
{
  "script": "string",           // Required: Manim script
  "scene_name": "string",       // Optional: Scene class name
  "entry_id": "string",         // Optional: Database entry ID
  "priority": 0                 // Optional: Job priority (0-10)
}
```

### Enhanced Batch Render Endpoint

**POST** `/batch_render`

```json
{
  "scripts": [
    {
      "script": "string",
      "scene_name": "string",
      "entry_id": "string"       // Optional: Individual entry ID
    }
  ],
  "entry_id": "string",         // Optional: Global entry ID for merged video
  "priority": 0
}
```

## Error Handling

The system is designed to be resilient:

- **Cloudinary Unavailable**: Videos are still saved locally
- **Database API Unavailable**: Rendering continues, but database is not updated
- **Upload Failures**: Logged but don't fail the entire job
- **Configuration Missing**: Graceful degradation to local-only storage

## Testing

Run the test suite to verify the setup:

```bash
cd backend
python test_cloudinary_workflow.py
```

The test will verify:
- Configuration is correct
- Cloudinary connectivity
- Database API accessibility
- Job processing
- Full workflow integration

## Monitoring

Check logs for upload status:

```bash
# Successful upload
INFO - Video uploaded to Cloudinary and database updated for entry abc123

# Configuration issues
WARNING - Cloudinary not configured, skipping upload
WARNING - Backend API base URL not configured, skipping database update

# Upload failures
ERROR - Failed to upload to Cloudinary or update database: [error details]
```

## Production Considerations

1. **Security**: Use environment variables for all credentials
2. **Monitoring**: Set up alerts for upload failures
3. **Backup**: Maintain local copies as backup
4. **Rate Limits**: Consider Cloudinary rate limits for high-volume usage
5. **Storage**: Configure Cloudinary storage policies
6. **CDN**: Leverage Cloudinary's CDN for global video delivery

## Troubleshooting

### Common Issues

1. **"Cloudinary not configured"**
   - Check environment variables are set correctly
   - Verify credentials in Cloudinary dashboard

2. **"Cannot connect to database API"**
   - Ensure frontend server is running
   - Check `BACKEND_API_BASE_URL` is correct
   - Verify `/api/entries/update` endpoint exists

3. **"Upload failed"**
   - Check Cloudinary credentials
   - Verify network connectivity
   - Check Cloudinary account limits

4. **"Database update failed"**
   - Check frontend API is accessible
   - Verify entry ID exists in database
   - Check API endpoint accepts the correct format

### Debug Commands

```bash
# Test configuration
curl http://localhost:8000/debug/config

# Test job processing
curl -X POST http://localhost:8000/debug/test-worker

# Check job status
curl http://localhost:8000/jobs/{job_id}

# View queue statistics
curl http://localhost:8000/queues/stats
```

## Migration from Local-Only

If upgrading from local-only storage:

1. Add Cloudinary configuration to `.env`
2. Restart the backend server
3. New jobs will automatically use Cloudinary
4. Existing local videos remain accessible
5. Optionally migrate existing videos to Cloudinary

The system maintains backward compatibility - jobs without `entry_id` work as before.
