{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/app/register/page.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { CivicAuthIframeContainer } from \"@civic/auth/react\";\r\nimport { redirect } from \"next/navigation\";\r\n\r\nexport default function Login (){\r\n  return (\r\n      <div className=\"purple-gradient relative min-h-screen overflow-hidden bg-background\">\r\n        <div>\r\n          <CivicAuthIframeContainer\r\n            onClose={() => {\r\n              redirect(\"/\");\r\n            }}\r\n            closeOnRedirect={true}\r\n          />\r\n        </div>\r\n      </div>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;AAHA;;;;AAKe,SAAS;IACtB,qBACI,6LAAC;QAAI,WAAU;kBACb,cAAA,6LAAC;sBACC,cAAA,6LAAC,8LAAA,CAAA,2BAAwB;gBACvB,SAAS;oBACP,CAAA,GAAA,qIAAA,CAAA,WAAQ,AAAD,EAAE;gBACX;gBACA,iBAAiB;;;;;;;;;;;;;;;;AAK7B;KAbwB", "debugId": null}}]}