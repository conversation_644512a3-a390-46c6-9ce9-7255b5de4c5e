import json
import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional
from qstash import QStash
from qstash.receiver import Receiver

from config.queue_config import get_queue_config
from models.job_models import (
    JobRequest,
    JobStatusTracker,
    JobStatus,
    JobType,
    JobResult,
    QueueStats,
    RenderJobData,
    BatchRenderJobData,
)

logger = logging.getLogger(__name__)


class QueueManager:
    """Manages job queuing and status tracking using Upstash QStash."""

    def __init__(self):
        self.config = get_queue_config()
        self.client = QStash(self.config.qstash_token)
        self.receiver = Receiver(
            current_signing_key=self.config.qstash_current_signing_key,
            next_signing_key=self.config.qstash_next_signing_key,
        )

        # In-memory job status tracking (in production, use Redis or database)
        self._job_statuses: Dict[str, JobStatusTracker] = {}

        # Initialize queues
        self._initialize_queues()

    def _initialize_queues(self):
        """Initialize QStash queues with proper configuration."""
        try:
            # For now, we'll skip explicit queue creation as the Python SDK
            # will create queues automatically when we first enqueue messages
            logger.info(
                "Queue initialization skipped - queues will be created on first use"
            )
        except Exception as e:
            logger.error(f"Failed to initialize queues: {e}")
            raise

    def queue_render_job(
        self, script: str, scene_name: Optional[str] = None, priority: int = 0
    ) -> str:
        """Queue a single render job."""
        job_request = JobRequest(
            job_type=JobType.RENDER,
            render_data=RenderJobData(script=script, scene_name=scene_name),
            max_retries=self.config.max_retries,
            timeout_seconds=self.config.job_timeout_seconds,
        )

        return self._queue_job(job_request, self.config.render_queue_name, priority)

    def queue_batch_render_job(
        self, scripts: List[RenderJobData], priority: int = 0
    ) -> str:
        """Queue a batch render job."""
        job_request = JobRequest(
            job_type=JobType.BATCH_RENDER,
            batch_render_data=BatchRenderJobData(scripts=scripts),
            max_retries=self.config.max_retries,
            timeout_seconds=self.config.job_timeout_seconds,
        )

        return self._queue_job(
            job_request, self.config.batch_render_queue_name, priority
        )

    def _queue_job(
        self, job_request: JobRequest, queue_name: str, priority: int = 0
    ) -> str:
        """Internal method to queue a job."""
        try:
            # Determine worker endpoint based on job type
            # Ensure proper URL construction by removing trailing slash from base URL
            base_url = self.config.worker_base_url.rstrip("/")

            if job_request.job_type == JobType.RENDER:
                worker_endpoint = f"{base_url}/worker/render"
            elif job_request.job_type == JobType.BATCH_RENDER:
                worker_endpoint = f"{base_url}/worker/batch_render"
            else:
                raise ValueError(f"Unknown job type: {job_request.job_type}")

            # Send job to queue using enqueue_json
            # Convert datetime objects to ISO format strings for JSON serialization
            job_data = job_request.model_dump()
            if "created_at" in job_data and job_data["created_at"]:
                job_data["created_at"] = job_data["created_at"].isoformat()

            response = self.client.message.enqueue_json(
                queue=queue_name,
                url=worker_endpoint,
                body=job_data,
                headers={
                    "Content-Type": "application/json",
                    "X-Worker-Secret": self.config.worker_secret,
                },
                retries=job_request.max_retries,
            )

            # Track job status
            # Handle response - it might be a list or single response
            message_id = None
            try:
                if hasattr(response, "message_id"):
                    message_id = getattr(response, "message_id", None)
                elif (
                    isinstance(response, list)
                    and len(response) > 0
                    and hasattr(response[0], "message_id")
                ):
                    message_id = getattr(response[0], "message_id", None)
            except Exception:
                message_id = None

            job_status = JobStatusTracker(
                job_id=job_request.job_id,
                job_type=job_request.job_type,
                status=JobStatus.QUEUED,
                created_at=job_request.created_at,
                max_retries=job_request.max_retries,
                queue_name=queue_name,
                message_id=message_id,
            )

            self._job_statuses[job_request.job_id] = job_status

            logger.info(
                f"Job {job_request.job_id} queued successfully with message ID {message_id}"
            )
            return job_request.job_id

        except Exception as e:
            logger.error(f"Failed to queue job {job_request.job_id}: {e}")
            raise

    def get_job_status(self, job_id: str) -> Optional[JobStatusTracker]:
        """Get the status of a specific job."""
        return self._job_statuses.get(job_id)

    def update_job_status(
        self,
        job_id: str,
        status: JobStatus,
        result: Optional[JobResult] = None,
        error_message: Optional[str] = None,
    ):
        """Update job status."""
        if job_id not in self._job_statuses:
            logger.warning(f"Attempted to update unknown job: {job_id}")
            return

        job_status = self._job_statuses[job_id]
        job_status.status = status

        if status == JobStatus.PROCESSING and not job_status.started_at:
            job_status.started_at = datetime.now(timezone.utc)

        if status in [JobStatus.COMPLETED, JobStatus.FAILED, JobStatus.CANCELLED]:
            job_status.completed_at = datetime.now(timezone.utc)

        if result:
            job_status.result = result

        if error_message:
            job_status.last_error = error_message
            job_status.error_count += 1

        if status == JobStatus.FAILED:
            job_status.current_retry += 1

        logger.info(f"Job {job_id} status updated to {status}")

    def get_queue_stats(self, queue_name: str) -> QueueStats:
        """Get statistics for a specific queue."""
        stats = QueueStats(queue_name=queue_name)

        for job_status in self._job_statuses.values():
            if job_status.queue_name != queue_name:
                continue

            stats.total_jobs += 1

            if job_status.status == JobStatus.QUEUED:
                stats.queued_jobs += 1
            elif job_status.status == JobStatus.PROCESSING:
                stats.processing_jobs += 1
            elif job_status.status == JobStatus.COMPLETED:
                stats.completed_jobs += 1
            elif job_status.status == JobStatus.FAILED:
                stats.failed_jobs += 1
            elif job_status.status == JobStatus.CANCELLED:
                stats.cancelled_jobs += 1

        return stats

    def list_jobs(
        self,
        status_filter: Optional[JobStatus] = None,
        job_type_filter: Optional[JobType] = None,
        limit: int = 100,
    ) -> List[JobStatusTracker]:
        """List jobs with optional filtering."""
        jobs = list(self._job_statuses.values())

        if status_filter:
            jobs = [job for job in jobs if job.status == status_filter]

        if job_type_filter:
            jobs = [job for job in jobs if job.job_type == job_type_filter]

        # Sort by creation time (newest first)
        jobs.sort(key=lambda x: x.created_at, reverse=True)

        return jobs[:limit]

    def cancel_job(self, job_id: str) -> bool:
        """Cancel a job if it's not already processing or completed."""
        job_status = self.get_job_status(job_id)
        if not job_status:
            return False

        if job_status.status in [JobStatus.COMPLETED, JobStatus.PROCESSING]:
            return False

        self.update_job_status(job_id, JobStatus.CANCELLED)
        return True

    def verify_webhook_signature(self, body: str, signature: str, url: str) -> bool:
        """Verify webhook signature from QStash."""
        try:
            self.receiver.verify(body=body, signature=signature, url=url)
            return True
        except Exception as e:
            logger.error(f"Webhook signature verification failed: {e}")
            return False

    def retry_failed_job(self, job_id: str) -> bool:
        """Retry a failed job if it can be retried."""
        job_status = self.get_job_status(job_id)
        if not job_status or not job_status.can_retry():
            return False

        try:
            # Reset job status for retry
            job_status.status = JobStatus.QUEUED
            job_status.last_error = None

            # Re-queue the job (this would need the original job request)
            # For now, we'll just update the status
            logger.info(
                f"Job {job_id} marked for retry (attempt {job_status.current_retry + 1})"
            )
            return True

        except Exception as e:
            logger.error(f"Failed to retry job {job_id}: {e}")
            return False

    def handle_dead_letter_job(self, job_id: str, final_error: str):
        """Handle a job that has exhausted all retries."""
        job_status = self.get_job_status(job_id)
        if not job_status:
            return

        logger.error(
            f"Job {job_id} moved to dead letter queue after {job_status.current_retry} retries. Final error: {final_error}"
        )

        # Update job status to failed with final error
        self.update_job_status(
            job_id, JobStatus.FAILED, error_message=f"Dead letter: {final_error}"
        )

        # In a production system, you might want to:
        # 1. Store the job in a separate dead letter queue
        # 2. Send notifications to administrators
        # 3. Log to external monitoring systems

    def cleanup_completed_jobs(self, older_than_hours: int = 24):
        """Clean up completed jobs older than specified hours."""
        from datetime import timedelta

        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=older_than_hours)
        jobs_to_remove = []

        for job_id, job_status in self._job_statuses.items():
            if (
                job_status.status == JobStatus.COMPLETED
                and job_status.completed_at
                and job_status.completed_at < cutoff_time
            ):
                jobs_to_remove.append(job_id)

        for job_id in jobs_to_remove:
            del self._job_statuses[job_id]
            logger.info(f"Cleaned up completed job: {job_id}")

        return len(jobs_to_remove)

    def get_failed_jobs(self) -> List[JobStatusTracker]:
        """Get all failed jobs that can potentially be retried."""
        return [
            job
            for job in self._job_statuses.values()
            if job.status == JobStatus.FAILED and job.can_retry()
        ]


# Global queue manager instance
_queue_manager: Optional[QueueManager] = None


def get_queue_manager() -> QueueManager:
    """Get the global queue manager instance."""
    global _queue_manager
    if _queue_manager is None:
        _queue_manager = QueueManager()
    return _queue_manager
