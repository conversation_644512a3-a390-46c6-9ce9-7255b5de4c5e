#!/usr/bin/env python3
"""
Test script for the enhanced Cloudinary workflow.
Tests the complete pipeline: render → upload to Cloudinary → update database entry.
"""

import os
import sys
import asyncio
import logging
import requests
from datetime import datetime, timezone

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.job_models import JobRequest, JobType, RenderJobData
from workers.job_processor import get_job_processor
from services.cloudinary_service import get_cloudinary_service
from config.queue_config import get_queue_config

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Test script for Manim
TEST_SCRIPT = """from manim import *

class CloudinaryTest(Scene):
    def construct(self):
        title = Text("Cloudinary Upload Test", font_size=48)
        subtitle = Text("Testing video upload workflow", font_size=24)
        subtitle.next_to(title, DOWN, buff=0.5)
        
        self.play(Write(title))
        self.wait(0.5)
        self.play(Write(subtitle))
        self.wait(1)
        
        # Create a simple animation
        circle = Circle(radius=1, color=BLUE)
        square = Square(side_length=2, color=RED)
        
        self.play(Create(circle))
        self.wait(0.5)
        self.play(Transform(circle, square))
        self.wait(1)
"""

def test_configuration():
    """Test if all required configuration is present."""
    logger.info("Testing configuration...")
    
    config = get_queue_config()
    
    # Check Cloudinary configuration
    cloudinary_service = get_cloudinary_service()
    if not cloudinary_service.is_configured():
        logger.error("Cloudinary is not properly configured!")
        logger.error("Please set CLOUDINARY_CLOUD_NAME, CLOUDINARY_API_KEY, and CLOUDINARY_API_SECRET")
        return False
    
    # Check backend API configuration
    if not config.backend_api_base_url:
        logger.error("Backend API base URL is not configured!")
        logger.error("Please set BACKEND_API_BASE_URL")
        return False
    
    logger.info("✓ Configuration looks good")
    return True

def test_cloudinary_upload():
    """Test Cloudinary upload functionality."""
    logger.info("Testing Cloudinary upload...")
    
    try:
        cloudinary_service = get_cloudinary_service()
        
        # Create a dummy video file for testing
        test_video_path = "test_video.mp4"
        
        # Create a minimal test video using ffmpeg (if available)
        try:
            import subprocess
            subprocess.run([
                "ffmpeg", "-f", "lavfi", "-i", "testsrc=duration=2:size=320x240:rate=1",
                "-y", test_video_path
            ], check=True, capture_output=True)
            
            # Upload to Cloudinary
            result = cloudinary_service.upload_video(
                test_video_path,
                public_id="test/cloudinary_workflow_test",
                folder="test-uploads"
            )
            
            logger.info(f"✓ Video uploaded successfully: {result.get('secure_url')}")
            
            # Clean up
            os.remove(test_video_path)
            
            # Clean up Cloudinary
            cloudinary_service.delete_video("test/cloudinary_workflow_test")
            
            return True
            
        except subprocess.CalledProcessError:
            logger.warning("FFmpeg not available, skipping video upload test")
            return True
        except FileNotFoundError:
            logger.warning("FFmpeg not found, skipping video upload test")
            return True
            
    except Exception as e:
        logger.error(f"Cloudinary upload test failed: {e}")
        return False

def test_database_api():
    """Test the database API endpoint."""
    logger.info("Testing database API...")
    
    try:
        config = get_queue_config()
        api_url = f"{config.backend_api_base_url}/api/entries/update"
        
        # Test with dummy data
        payload = {
            "entryId": "test-entry-id",
            "videoURL": "https://test.cloudinary.com/test-video.mp4"
        }
        
        response = requests.post(
            api_url,
            json=payload,
            headers={"Content-Type": "application/json"},
            timeout=10
        )
        
        if response.status_code == 404:
            logger.info("✓ Database API is reachable (entry not found is expected)")
            return True
        elif response.status_code == 200:
            logger.info("✓ Database API is working")
            return True
        else:
            logger.warning(f"Database API returned status {response.status_code}")
            return True
            
    except requests.exceptions.ConnectionError:
        logger.error("Cannot connect to database API. Is the frontend server running?")
        return False
    except Exception as e:
        logger.error(f"Database API test failed: {e}")
        return False

def test_job_processor():
    """Test the job processor with a simple render job."""
    logger.info("Testing job processor...")
    
    try:
        # Create a test job without entry_id first
        job_request = JobRequest(
            job_type=JobType.RENDER,
            render_data=RenderJobData(
                script=TEST_SCRIPT,
                scene_name="CloudinaryTest"
            )
        )
        
        # Process the job
        job_processor = get_job_processor()
        result = job_processor.process_job(job_request)
        
        if result.success:
            logger.info(f"✓ Job processed successfully: {result.output_urls}")
            
            # Clean up generated files
            import shutil
            media_dir = f"media/{job_request.job_id}"
            if os.path.exists(media_dir):
                shutil.rmtree(media_dir)
                
            return True
        else:
            logger.error(f"Job processing failed: {result.error_message}")
            return False
            
    except Exception as e:
        logger.error(f"Job processor test failed: {e}")
        return False

def test_full_workflow():
    """Test the complete workflow with Cloudinary upload and database update."""
    logger.info("Testing full workflow with Cloudinary upload...")
    
    try:
        # Create a test job with entry_id
        job_request = JobRequest(
            job_type=JobType.RENDER,
            render_data=RenderJobData(
                script=TEST_SCRIPT,
                scene_name="CloudinaryTest",
                entry_id="test-entry-workflow"  # This will trigger Cloudinary upload
            )
        )
        
        # Process the job
        job_processor = get_job_processor()
        result = job_processor.process_job(job_request)
        
        if result.success:
            logger.info(f"✓ Full workflow completed successfully!")
            logger.info(f"Output URLs: {result.output_urls}")
            
            # Check if Cloudinary URL is in the output
            cloudinary_urls = [url for url in result.output_urls if "cloudinary.com" in url]
            if cloudinary_urls:
                logger.info(f"✓ Video uploaded to Cloudinary: {cloudinary_urls[0]}")
            else:
                logger.warning("No Cloudinary URL found in output (this is expected if Cloudinary is not configured)")
            
            # Clean up generated files
            import shutil
            media_dir = f"media/{job_request.job_id}"
            if os.path.exists(media_dir):
                shutil.rmtree(media_dir)
                
            return True
        else:
            logger.error(f"Full workflow failed: {result.error_message}")
            return False
            
    except Exception as e:
        logger.error(f"Full workflow test failed: {e}")
        return False

def main():
    """Run all tests."""
    logger.info("Starting Cloudinary workflow tests...")
    logger.info("=" * 50)
    
    tests = [
        ("Configuration", test_configuration),
        ("Cloudinary Upload", test_cloudinary_upload),
        ("Database API", test_database_api),
        ("Job Processor", test_job_processor),
        ("Full Workflow", test_full_workflow),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                logger.info(f"✓ {test_name} PASSED")
            else:
                logger.error(f"✗ {test_name} FAILED")
        except Exception as e:
            logger.error(f"✗ {test_name} FAILED with exception: {e}")
    
    logger.info("\n" + "=" * 50)
    logger.info(f"Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All tests passed! The Cloudinary workflow is ready.")
        return 0
    else:
        logger.error("❌ Some tests failed. Please check the configuration and setup.")
        return 1

if __name__ == "__main__":
    exit(main())
