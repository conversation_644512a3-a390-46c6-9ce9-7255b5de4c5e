{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/GitHub/EdgeX/clarifai/frontend/src/app/register/page.tsx"], "sourcesContent": ["\"use client\"\r\n\r\nimport { CivicAuthIframeContainer } from \"@civic/auth/react\";\r\nimport { redirect } from \"next/navigation\";\r\n\r\nexport default function Login (){\r\n  return (\r\n      <div className=\"purple-gradient relative min-h-screen overflow-hidden bg-background\">\r\n        <div>\r\n          <CivicAuthIframeContainer\r\n            onClose={() => {\r\n              redirect(\"/\");\r\n            }}\r\n            closeOnRedirect={true}\r\n          />\r\n        </div>\r\n      </div>\r\n  );\r\n};"], "names": [], "mappings": ";;;;AAEA;AAAA;AACA;;;;;;AAHA;;;;AAKe,SAAS;IACtB,qBACI,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;sBACC,cAAA,8OAAC,2LAAA,CAAA,2BAAwB;gBACvB,SAAS;oBACP,CAAA,GAAA,kIAAA,CAAA,WAAQ,AAAD,EAAE;gBACX;gBACA,iBAAiB;;;;;;;;;;;;;;;;AAK7B", "debugId": null}}]}