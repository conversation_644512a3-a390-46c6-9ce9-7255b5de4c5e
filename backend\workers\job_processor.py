import os
import uuid
import subprocess
import shutil
import logging
import requests
from datetime import datetime, timezone
from typing import List, Optional
from fastapi import HTTPException

from models.job_models import JobRequest, JobResult, JobStatus, JobType
from services.queue_manager import get_queue_manager
from services.cloudinary_service import get_cloudinary_service
from config.queue_config import get_queue_config

logger = logging.getLogger(__name__)


class JobProcessor:
    """Processes jobs from the message queue."""

    def __init__(self):
        self.queue_manager = get_queue_manager()
        os.makedirs("media", exist_ok=True)

    def process_job(self, job_request: JobRequest) -> JobResult:
        """Process a job based on its type."""
        start_time = datetime.now(timezone.utc)

        try:
            # Update job status to processing
            self.queue_manager.update_job_status(
                job_request.job_id, JobStatus.PROCESSING
            )

            if job_request.job_type == JobType.RENDER:
                result = self._process_render_job(job_request)
            elif job_request.job_type == JobType.BATCH_RENDER:
                result = self._process_batch_render_job(job_request)
            else:
                raise ValueError(f"Unknown job type: {job_request.job_type}")

            # Calculate processing time
            end_time = datetime.now(timezone.utc)
            processing_time = (end_time - start_time).total_seconds()

            result.processing_time_seconds = processing_time
            result.completed_at = end_time

            # Update job status to completed
            self.queue_manager.update_job_status(
                job_request.job_id, JobStatus.COMPLETED, result=result
            )

            logger.info(
                f"Job {job_request.job_id} completed successfully in {processing_time:.2f}s"
            )
            return result

        except Exception as e:
            error_message = str(e)
            logger.error(f"Job {job_request.job_id} failed: {error_message}")

            # Create failed result
            result = JobResult(
                job_id=job_request.job_id,
                success=False,
                error_message=error_message,
                completed_at=datetime.now(timezone.utc),
            )

            # Update job status to failed
            self.queue_manager.update_job_status(
                job_request.job_id,
                JobStatus.FAILED,
                result=result,
                error_message=error_message,
            )

            return result

    def _process_render_job(self, job_request: JobRequest) -> JobResult:
        """Process a single render job."""
        if not job_request.render_data:
            raise ValueError("Render data is required for render job")

        render_data = job_request.render_data
        job_id = job_request.job_id

        # Create temporary directory
        tmp_dir = os.path.join(os.getcwd(), "tmp")
        os.makedirs(tmp_dir, exist_ok=True)

        try:
            # Write script to file
            script_filename = f"script_{job_id}.py"
            script_path = os.path.join(tmp_dir, script_filename)

            with open(script_path, "w", encoding="utf-8") as f:
                f.write(render_data.script)

            # Determine scene name
            scene = render_data.scene_name or self._get_scene_name(render_data.script)

            # Run manim command
            cmd = f"manim -qh {script_filename} {scene}"

            subprocess.run(
                cmd,
                shell=True,
                cwd=tmp_dir,
                capture_output=True,
                text=True,
                check=True,
                encoding="utf-8",
                errors="replace",
            )

            # Locate rendered video
            output_video_path = self._find_rendered_video(tmp_dir, job_id, scene)

            if not output_video_path or not os.path.exists(output_video_path):
                raise FileNotFoundError("Rendered video not found")

            # Move to final media folder
            final_output_dir = os.path.join("media", job_id)
            os.makedirs(final_output_dir, exist_ok=True)
            final_output_path = os.path.join(final_output_dir, f"{scene}.mp4")
            shutil.move(output_video_path, final_output_path)

            # Upload to Cloudinary and update database if entry_id is provided
            cloudinary_url = None
            if render_data.entry_id:
                try:
                    cloudinary_url = self._upload_and_update_entry(
                        final_output_path, job_id, scene, render_data.entry_id
                    )
                    logger.info(
                        f"Video uploaded to Cloudinary and database updated for entry {render_data.entry_id}"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to upload to Cloudinary or update database: {e}"
                    )
                    # Don't fail the job if upload fails, just log the error

            output_urls = [f"/media/{job_id}/{scene}.mp4"]
            if cloudinary_url:
                output_urls.append(cloudinary_url)

            return JobResult(
                job_id=job_id,
                success=True,
                output_urls=output_urls,
            )

        finally:
            # Clean up temporary files
            self._clear_tmp_contents(tmp_dir)

    def _process_batch_render_job(self, job_request: JobRequest) -> JobResult:
        """Process a batch render job."""
        if not job_request.batch_render_data:
            raise ValueError("Batch render data is required for batch render job")

        batch_data = job_request.batch_render_data
        job_id = job_request.job_id

        # Create temporary directory
        tmp_dir = os.path.join(os.getcwd(), "tmp")
        os.makedirs(tmp_dir, exist_ok=True)

        rendered_video_paths = []

        try:
            # Process each script
            for idx, script_req in enumerate(batch_data.scripts):
                script_filename = f"script_{job_id}_{idx}.py"
                script_path = os.path.join(tmp_dir, script_filename)

                with open(script_path, "w", encoding="utf-8") as f:
                    f.write(script_req.script)

                scene = script_req.scene_name or self._get_scene_name(script_req.script)
                cmd = f"manim -qh {script_filename} {scene}"

                subprocess.run(
                    cmd,
                    shell=True,
                    cwd=tmp_dir,
                    capture_output=True,
                    text=True,
                    check=True,
                    encoding="utf-8",
                    errors="replace",
                )

                # Find rendered video
                output_path = self._find_rendered_video(
                    tmp_dir, f"{job_id}_{idx}", scene
                )
                if not output_path or not os.path.exists(output_path):
                    raise FileNotFoundError(
                        f"Rendered video for script {idx + 1} not found"
                    )

                rendered_video_paths.append(output_path)

            # Merge videos using ffmpeg
            merged_output_path = self._merge_videos(
                tmp_dir, rendered_video_paths, job_id
            )

            # Upload to Cloudinary and update database if entry_id is provided
            cloudinary_url = None
            if batch_data.entry_id:
                try:
                    cloudinary_url = self._upload_and_update_entry(
                        merged_output_path, job_id, "merged_output", batch_data.entry_id
                    )
                    logger.info(
                        f"Batch video uploaded to Cloudinary and database updated for entry {batch_data.entry_id}"
                    )
                except Exception as e:
                    logger.error(
                        f"Failed to upload batch video to Cloudinary or update database: {e}"
                    )
                    # Don't fail the job if upload fails, just log the error

            output_urls = [f"/media/{job_id}/merged_output.mp4"]
            if cloudinary_url:
                output_urls.append(cloudinary_url)

            return JobResult(
                job_id=job_id,
                success=True,
                output_urls=output_urls,
            )

        finally:
            # Clean up temporary files
            self._clear_tmp_contents(tmp_dir)

    def _get_scene_name(self, script: str) -> str:
        """Extract scene name from script."""
        lines = script.splitlines()
        for line in lines:
            if line.strip().startswith("class ") and "Scene" in line:
                return line.split("(")[0].replace("class ", "").strip()
        return "GenScene"

    def _find_rendered_video(
        self, tmp_dir: str, job_id: str, scene: str
    ) -> Optional[str]:
        """Find the rendered video file."""
        media_root = os.path.join(tmp_dir, "media")

        for root, _, files in os.walk(media_root):
            if f"script_{job_id}" in root:
                for file in files:
                    if (
                        file.endswith(f"{scene}.mp4")
                        and "partial_movie_files" not in root
                    ):
                        return os.path.join(root, file)

        return None

    def _merge_videos(self, tmp_dir: str, video_paths: List[str], job_id: str) -> str:
        """Merge multiple videos into one."""
        # Create list.txt for ffmpeg concat
        concat_list_path = os.path.join(tmp_dir, "list.txt")
        with open(concat_list_path, "w", encoding="utf-8") as f:
            for path in video_paths:
                f.write(f"file '{path}'\n")

        # Create final output directory
        final_output_dir = os.path.join("media", job_id)
        os.makedirs(final_output_dir, exist_ok=True)
        merged_output_path = os.path.join(final_output_dir, "merged_output.mp4")

        # Run ffmpeg merge command
        subprocess.run(
            f"ffmpeg -f concat -safe 0 -i list.txt -c copy merged_output.mp4",
            shell=True,
            cwd=tmp_dir,
            capture_output=True,
            text=True,
            check=True,
        )

        # Move merged video to final location
        shutil.move(os.path.join(tmp_dir, "merged_output.mp4"), merged_output_path)

        return merged_output_path

    def _upload_and_update_entry(
        self, video_path: str, job_id: str, scene_name: str, entry_id: str
    ) -> str:
        """Upload video to Cloudinary and update database entry."""
        try:
            # Upload to Cloudinary
            cloudinary_service = get_cloudinary_service()

            if not cloudinary_service.is_configured():
                logger.warning("Cloudinary not configured, skipping upload")
                return ""

            cloudinary_url = cloudinary_service.upload_video_from_job(
                video_path, job_id, scene_name
            )

            # Update database entry
            config = get_queue_config()
            if config.backend_api_base_url:
                self._update_database_entry(entry_id, cloudinary_url)
            else:
                logger.warning(
                    "Backend API base URL not configured, skipping database update"
                )

            return cloudinary_url

        except Exception as e:
            logger.error(f"Failed to upload and update entry: {e}")
            raise

    def _update_database_entry(self, entry_id: str, video_url: str):
        """Update database entry with video URL."""
        try:
            config = get_queue_config()
            api_url = f"{config.backend_api_base_url}/api/entries/update"

            payload = {"entryId": entry_id, "videoURL": video_url}

            response = requests.post(
                api_url,
                json=payload,
                headers={"Content-Type": "application/json"},
                timeout=30,
            )

            response.raise_for_status()
            logger.info(
                f"Successfully updated database entry {entry_id} with video URL"
            )

        except requests.exceptions.RequestException as e:
            logger.error(f"Failed to update database entry {entry_id}: {e}")
            raise

    def _clear_tmp_contents(self, tmp_dir: str):
        """Clean up temporary directory contents."""
        try:
            for item in os.listdir(tmp_dir):
                item_path = os.path.join(tmp_dir, item)
                if os.path.isfile(item_path):
                    os.remove(item_path)
                elif os.path.isdir(item_path):
                    shutil.rmtree(item_path)
        except Exception as e:
            logger.warning(f"Failed to clean tmp directory: {e}")


# Global job processor instance
_job_processor: Optional[JobProcessor] = None


def get_job_processor() -> JobProcessor:
    """Get the global job processor instance."""
    global _job_processor
    if _job_processor is None:
        _job_processor = JobProcessor()
    return _job_processor
