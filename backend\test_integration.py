#!/usr/bin/env python3
"""
Integration test for the enhanced workflow without external dependencies.
Tests the code paths and data flow without requiring Cloudinary or database connections.
"""

import os
import sys
import logging
from unittest.mock import Mock, patch

# Add the backend directory to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from models.job_models import (
    JobRequest, JobType, RenderJobData, BatchRenderJobData,
    CreateRenderJobRequest, CreateBatchRenderJobRequest
)
from services.queue_manager import get_queue_manager
from workers.job_processor import get_job_processor

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_model_enhancements():
    """Test that the models include the new entry_id fields."""
    logger.info("Testing model enhancements...")
    
    # Test RenderJobData with entry_id
    render_data = RenderJobData(
        script="test script",
        scene_name="TestScene",
        entry_id="test-entry-123"
    )
    assert render_data.entry_id == "test-entry-123"
    logger.info("✓ RenderJobData supports entry_id")
    
    # Test BatchRenderJobData with entry_id
    batch_data = BatchRenderJobData(
        scripts=[render_data],
        entry_id="batch-entry-456"
    )
    assert batch_data.entry_id == "batch-entry-456"
    logger.info("✓ BatchRenderJobData supports entry_id")
    
    # Test CreateRenderJobRequest with entry_id
    render_request = CreateRenderJobRequest(
        script="test script",
        scene_name="TestScene",
        entry_id="request-entry-789",
        priority=1
    )
    assert render_request.entry_id == "request-entry-789"
    logger.info("✓ CreateRenderJobRequest supports entry_id")
    
    # Test CreateBatchRenderJobRequest with entry_id
    batch_request = CreateBatchRenderJobRequest(
        scripts=[render_data],
        entry_id="batch-request-101",
        priority=2
    )
    assert batch_request.entry_id == "batch-request-101"
    logger.info("✓ CreateBatchRenderJobRequest supports entry_id")
    
    return True

def test_queue_manager_enhancements():
    """Test that the queue manager accepts entry_id parameters."""
    logger.info("Testing queue manager enhancements...")
    
    try:
        queue_manager = get_queue_manager()
        
        # Test queue_render_job with entry_id
        with patch.object(queue_manager, '_queue_job') as mock_queue:
            mock_queue.return_value = "test-job-id"
            
            job_id = queue_manager.queue_render_job(
                script="test script",
                scene_name="TestScene",
                entry_id="test-entry-123",
                priority=1
            )
            
            # Verify the job was queued with correct data
            mock_queue.assert_called_once()
            job_request = mock_queue.call_args[0][0]
            assert job_request.render_data.entry_id == "test-entry-123"
            logger.info("✓ queue_render_job supports entry_id")
        
        # Test queue_batch_render_job with entry_id
        with patch.object(queue_manager, '_queue_job') as mock_queue:
            mock_queue.return_value = "test-batch-job-id"
            
            scripts = [RenderJobData(script="test", entry_id="script-entry")]
            job_id = queue_manager.queue_batch_render_job(
                scripts=scripts,
                entry_id="batch-entry-456",
                priority=2
            )
            
            # Verify the job was queued with correct data
            mock_queue.assert_called_once()
            job_request = mock_queue.call_args[0][0]
            assert job_request.batch_render_data.entry_id == "batch-entry-456"
            logger.info("✓ queue_batch_render_job supports entry_id")
        
        return True
        
    except Exception as e:
        logger.error(f"Queue manager test failed: {e}")
        return False

def test_job_processor_enhancements():
    """Test that the job processor handles entry_id correctly."""
    logger.info("Testing job processor enhancements...")
    
    try:
        job_processor = get_job_processor()
        
        # Mock the upload and update methods to avoid external dependencies
        with patch.object(job_processor, '_upload_and_update_entry') as mock_upload:
            mock_upload.return_value = "https://cloudinary.com/test-video.mp4"
            
            # Mock the actual rendering process
            with patch.object(job_processor, '_find_rendered_video') as mock_find:
                mock_find.return_value = None  # This will cause the job to fail, but that's OK for testing
                
                # Create a job with entry_id
                job_request = JobRequest(
                    job_type=JobType.RENDER,
                    render_data=RenderJobData(
                        script="test script",
                        scene_name="TestScene",
                        entry_id="test-entry-123"
                    )
                )
                
                # Process the job (it will fail at rendering, but we can check the flow)
                try:
                    result = job_processor.process_job(job_request)
                except Exception:
                    pass  # Expected to fail at rendering
                
                # The upload method should not be called since rendering failed
                mock_upload.assert_not_called()
                logger.info("✓ Job processor handles entry_id in render jobs")
        
        # Test batch job with entry_id
        with patch.object(job_processor, '_upload_and_update_entry') as mock_upload:
            mock_upload.return_value = "https://cloudinary.com/test-batch-video.mp4"
            
            with patch.object(job_processor, '_find_rendered_video') as mock_find:
                mock_find.return_value = None
                
                # Create a batch job with entry_id
                job_request = JobRequest(
                    job_type=JobType.BATCH_RENDER,
                    batch_render_data=BatchRenderJobData(
                        scripts=[RenderJobData(script="test", entry_id="script-entry")],
                        entry_id="batch-entry-456"
                    )
                )
                
                # Process the job
                try:
                    result = job_processor.process_job(job_request)
                except Exception:
                    pass  # Expected to fail at rendering
                
                mock_upload.assert_not_called()
                logger.info("✓ Job processor handles entry_id in batch jobs")
        
        return True
        
    except Exception as e:
        logger.error(f"Job processor test failed: {e}")
        return False

def test_cloudinary_service():
    """Test that the Cloudinary service is properly structured."""
    logger.info("Testing Cloudinary service...")
    
    try:
        from services.cloudinary_service import get_cloudinary_service
        
        cloudinary_service = get_cloudinary_service()
        
        # Test configuration check
        is_configured = cloudinary_service.is_configured()
        logger.info(f"✓ Cloudinary service configuration check: {is_configured}")
        
        # Test method existence
        assert hasattr(cloudinary_service, 'upload_video')
        assert hasattr(cloudinary_service, 'upload_video_from_job')
        assert hasattr(cloudinary_service, 'delete_video')
        logger.info("✓ Cloudinary service has required methods")
        
        return True
        
    except Exception as e:
        logger.error(f"Cloudinary service test failed: {e}")
        return False

def test_configuration_structure():
    """Test that the configuration includes new fields."""
    logger.info("Testing configuration structure...")
    
    try:
        from config.queue_config import get_queue_config
        
        config = get_queue_config()
        
        # Test Cloudinary configuration fields
        assert hasattr(config, 'cloudinary_cloud_name')
        assert hasattr(config, 'cloudinary_api_key')
        assert hasattr(config, 'cloudinary_api_secret')
        assert hasattr(config, 'cloudinary_upload_preset')
        logger.info("✓ Configuration includes Cloudinary fields")
        
        # Test backend API configuration
        assert hasattr(config, 'backend_api_base_url')
        logger.info("✓ Configuration includes backend API field")
        
        return True
        
    except Exception as e:
        logger.error(f"Configuration test failed: {e}")
        return False

def main():
    """Run all integration tests."""
    logger.info("Starting integration tests...")
    logger.info("=" * 50)
    
    tests = [
        ("Model Enhancements", test_model_enhancements),
        ("Queue Manager Enhancements", test_queue_manager_enhancements),
        ("Job Processor Enhancements", test_job_processor_enhancements),
        ("Cloudinary Service", test_cloudinary_service),
        ("Configuration Structure", test_configuration_structure),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        logger.info(f"\n--- {test_name} ---")
        try:
            if test_func():
                passed += 1
                logger.info(f"✓ {test_name} PASSED")
            else:
                logger.error(f"✗ {test_name} FAILED")
        except Exception as e:
            logger.error(f"✗ {test_name} FAILED with exception: {e}")
            import traceback
            traceback.print_exc()
    
    logger.info("\n" + "=" * 50)
    logger.info(f"Integration Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        logger.info("🎉 All integration tests passed! The enhanced workflow is properly implemented.")
        return 0
    else:
        logger.error("❌ Some integration tests failed. Please check the implementation.")
        return 1

if __name__ == "__main__":
    exit(main())
