import os
import logging
from typing import Optional, Dict, Any
import cloudinary
import cloudinary.uploader
from config.queue_config import get_queue_config

logger = logging.getLogger(__name__)


class CloudinaryService:
    """Service for uploading videos to Cloudinary."""

    def __init__(self):
        self.config = get_queue_config()
        self._configure_cloudinary()

    def _configure_cloudinary(self):
        """Configure Cloudinary with API credentials."""
        try:
            cloudinary.config(
                cloud_name=self.config.cloudinary_cloud_name,
                api_key=self.config.cloudinary_api_key,
                api_secret=self.config.cloudinary_api_secret,
                secure=True,
            )
            logger.info("Cloudinary configured successfully")
        except Exception as e:
            logger.error(f"Failed to configure Cloudinary: {e}")
            raise

    def upload_video(
        self,
        video_path: str,
        public_id: Optional[str] = None,
        folder: Optional[str] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Upload a video file to Cloudinary.

        Args:
            video_path: Path to the video file to upload
            public_id: Custom public ID for the video (optional)
            folder: Folder to organize videos in Cloudinary (optional)
            **kwargs: Additional upload parameters

        Returns:
            Dict containing upload result with secure_url and other metadata

        Raises:
            Exception: If upload fails
        """
        try:
            if not os.path.exists(video_path):
                raise FileNotFoundError(f"Video file not found: {video_path}")

            # Prepare upload parameters
            upload_params = {
                "resource_type": "video",
                "use_filename": True,
                "unique_filename": False,
                "overwrite": True,
                **kwargs,
            }

            if public_id:
                upload_params["public_id"] = public_id

            if folder:
                upload_params["folder"] = folder

            # Use upload preset if configured (for unsigned uploads)
            if self.config.cloudinary_upload_preset:
                upload_params["upload_preset"] = self.config.cloudinary_upload_preset

            logger.info(f"Uploading video to Cloudinary: {video_path}")

            # Upload the video
            result = cloudinary.uploader.upload(video_path, **upload_params)

            logger.info(f"Video uploaded successfully. URL: {result.get('secure_url')}")

            return result

        except Exception as e:
            logger.error(f"Failed to upload video to Cloudinary: {e}")
            raise

    def upload_video_from_job(
        self, video_path: str, job_id: str, scene_name: str
    ) -> str:
        """
        Upload a video from a render job to Cloudinary.

        Args:
            video_path: Path to the rendered video file
            job_id: Job ID for organizing uploads
            scene_name: Scene name for the video

        Returns:
            Secure URL of the uploaded video

        Raises:
            Exception: If upload fails
        """
        try:
            # Create a meaningful public ID
            public_id = f"renders/{job_id}/{scene_name}"

            # Upload with job-specific parameters
            result = self.upload_video(
                video_path=video_path,
                public_id=public_id,
                folder="clarifai-renders",
                tags=["manim", "render", job_id],
                context={
                    "job_id": job_id,
                    "scene_name": scene_name,
                    "source": "clarifai-backend",
                },
            )

            return result["secure_url"]

        except Exception as e:
            logger.error(f"Failed to upload job video to Cloudinary: {e}")
            raise

    def delete_video(self, public_id: str) -> bool:
        """
        Delete a video from Cloudinary.

        Args:
            public_id: Public ID of the video to delete

        Returns:
            True if deletion was successful, False otherwise
        """
        try:
            result = cloudinary.uploader.destroy(
                public_id, resource_type="video", invalidate=True
            )

            success = result.get("result") == "ok"
            if success:
                logger.info(f"Video deleted successfully: {public_id}")
            else:
                logger.warning(f"Failed to delete video: {public_id}, result: {result}")

            return success

        except Exception as e:
            logger.error(f"Error deleting video from Cloudinary: {e}")
            return False

    def get_video_info(self, public_id: str) -> Optional[Dict[str, Any]]:
        """
        Get information about a video from Cloudinary.

        Args:
            public_id: Public ID of the video

        Returns:
            Video information dict or None if not found
        """
        try:
            result = cloudinary.api.resource(public_id, resource_type="video")
            return result

        except cloudinary.exceptions.NotFound:
            logger.warning(f"Video not found in Cloudinary: {public_id}")
            return None
        except Exception as e:
            logger.error(f"Error getting video info from Cloudinary: {e}")
            return None

    def is_configured(self) -> bool:
        """
        Check if Cloudinary is properly configured.

        Returns:
            True if all required configuration is present, False otherwise
        """
        return bool(
            self.config.cloudinary_cloud_name
            and self.config.cloudinary_api_key
            and self.config.cloudinary_api_secret
        )


# Global service instance
_cloudinary_service: Optional[CloudinaryService] = None


def get_cloudinary_service() -> CloudinaryService:
    """Get the global CloudinaryService instance."""
    global _cloudinary_service
    if _cloudinary_service is None:
        _cloudinary_service = CloudinaryService()
    return _cloudinary_service
