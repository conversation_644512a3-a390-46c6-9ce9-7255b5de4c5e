# QStash Configuration (Required)
QSTASH_TOKEN=your_qstash_token_from_upstash_console
QSTASH_CURRENT_SIGNING_KEY=your_current_signing_key
QSTASH_NEXT_SIGNING_KEY=your_next_signing_key

# Worker Configuration (Required)
WORKER_BASE_URL= https://sugar-keno-dvd-lease.trycloudflare.com  # Where QStash can reach your worker endpoints
WORKER_SECRET=yoyo

# Queue Names (Optional - defaults provided)
RENDER_QUEUE_NAME=render-jobs
BATCH_RENDER_QUEUE_NAME=batch-render-jobs

# Queue Settings (Optional - defaults provided)
QUEUE_PARALLELISM=1
MAX_RETRIES=3
JOB_TIMEOUT_SECONDS=300
CLEANUP_COMPLETED_JOBS_AFTER_HOURS=24

# Cloudinary Configuration (Required for video upload)
CLOUDINARY_CLOUD_NAME=your_cloudinary_cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret
CLOUDINARY_UPLOAD_PRESET=your_upload_preset  # Optional, for unsigned uploads

# Backend API Configuration (Required for database updates)
BACKEND_API_BASE_URL=http://localhost:3000  # URL to your frontend API

# Development Settings
# For development, use ngrok to expose your local server:
# ngrok http 8000
# Then set WORKER_BASE_URL=https://your-ngrok-url.ngrok.io
